using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Commands.Send;
using iPlatformExtension.MailCenter.Applications.Models.Common;
using iPlatformExtension.MailCenter.Applications.Queries.SysConfig;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System.Text;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Send
{
    /// <summary>
    /// 回复邮件命令处理器
    /// </summary>
    internal sealed class ReplyMailCommandHandler(
        IMailReceiveRepository mailReceiveRepository,
        IMailHostRepository mailHostRepository,
        IMailUserRepository mailUserRepository,
        IMailAttachmentsRepository mailAttachmentsRepository,
        IMailSignatureRepository mailSignatureRepository,
        IMailCorrelativeRepository mailCorrelativeRepository,
        IMediator mediator,
        IHttpContextAccessor httpContextAccessor,
        ILogger<ReplyMailCommandHandler> logger) : IRequestHandler<ReplyMailCommand, string>
    {
        public async Task<string> Handle(ReplyMailCommand request, CancellationToken cancellationToken)
        {
            // 1. 获取原邮件信息
            var originalMail = await mailReceiveRepository.Where(m => m.MailId == request.MailId)
                .FirstAsync(cancellationToken);
            if (originalMail == null)
            {
                throw new ArgumentException($"邮件ID {request.MailId} 不存在");
            }
            var originalMailUser = await mailUserRepository.Where(m => m.MailId == request.MailId)
                .ToListAsync(cancellationToken);
            if (originalMailUser == null)
            {
                throw new ArgumentException($"邮件ID {request.MailId} 不存在");
            }

            // 2. 获取当前用户信息
            var currentUserId = httpContextAccessor.HttpContext?.User?.GetUserID();
            if (string.IsNullOrEmpty(currentUserId))
            {
                throw new UnauthorizedAccessException("用户未登录");
            }

            // 3. 按优先级选择发件邮箱
            var selectedMailHost = await SelectReplyMailHost(originalMail, currentUserId, cancellationToken);
            if (selectedMailHost == null)
            {
                throw new ArgumentException("未找到可用的发件邮箱");
            }

            // 4. 构建邮件的内容（根据是否为转发来决定）
            string mailSubject;
            string mailBody;
            List<MailAddressList>? mailTo = null;
            List<MailAddressList>? mailCc = null;

            if (request.IsForward)
            {
                // 转发邮件
                mailSubject = BuildForwardSubject(originalMail.MailSubject);
                mailBody = BuildForwardBody(originalMail, originalMailUser);
                // 转发邮件不预设收件人和抄送人，由用户自己填写
            }
            else
            {
                // 回复邮件
                mailSubject = BuildReplySubject(originalMail.MailSubject);
                mailBody = BuildReplyBody(originalMail, originalMailUser);
                // 构建收件人和抄送人信息
                (mailTo, mailCc) = BuildReplyRecipients(originalMailUser, request.ReplyAll);
            }

            // 5. 构建发件人信息
            var mailFrom = new List<MailAddressList>
                {
                    new MailAddressList
                    {
                        MailAddress = selectedMailHost.Account,
                        DisplayName = selectedMailHost.ShowName
                    }
                };

            // 7. 获取用户签名（如果用户只有一个签名则自动添加）
            var signatureBody = await GetUserSignatureIfSingle(currentUserId, cancellationToken);

            // 8. 构建WriteMailCommand
            var writeMailCommand = new WriteMailCommand(
                MailId: null, // 新邮件
                MailFrom: mailFrom,
                MailTo: mailTo ?? new List<MailAddressList>(), // 转发邮件时为空列表，由用户填写
                MailCc: mailCc,
                MailBcc: null,
                MailSubject: mailSubject,
                MailHtmlBody: "", // 空的正文，用户需要自己填写
                HostId: selectedMailHost.HostId, // 使用选中的邮箱配置
                MailRelayBody: mailBody, // 将原邮件内容放在转发正文中
                IsImportant: false,
                IsRead: false,
                IsRequiredProcessTime: false,
                SendTime: null,
                Attachments: null,
                SignatureBody: signatureBody,
                RegionMailId: request.MailId
            );

            // 8. 调用WriteMail接口创建回复邮件
            var newMailId = await mediator.Send(writeMailCommand, cancellationToken);

            // 9. 添加关联邮件逻辑：关联当前收件与回复邮件
            await CreateMailCorrelationAsync(request.MailId, newMailId, currentUserId, cancellationToken);

            // 10. 复制原邮件的附件到新邮件
            if (request.IsForward)
            {
                // 转发邮件：根据附件大小决定附件类型
                await CopyMailAttachmentsForForward(request.MailId, newMailId, cancellationToken);
            }
            else
            {
                // 回复邮件：普通复制附件
                await CopyMailAttachments(request.MailId, newMailId, cancellationToken);
            }

            return newMailId;

        }

        /// <summary>
        /// 构建回复邮件主题
        /// </summary>
        /// <param name="originalSubject">原邮件主题</param>
        /// <returns>回复邮件主题</returns>
        private static string BuildReplySubject(string originalSubject)
        {
            if (string.IsNullOrEmpty(originalSubject))
            {
                return "Re: ";
            }

            return $"Re: {originalSubject}";
        }

        /// <summary>
        /// 构建转发邮件主题
        /// </summary>
        /// <param name="originalSubject">原邮件主题</param>
        /// <returns>转发邮件主题</returns>
        private static string BuildForwardSubject(string originalSubject)
        {
            if (string.IsNullOrEmpty(originalSubject))
            {
                return "Fw: ";
            }

            return $"Fw: {originalSubject}";
        }

        /// <summary>
        /// 构建回复邮件正文
        /// </summary>
        /// <param name="originalMail">原邮件信息</param>
        /// <param name="originalMailUsers">原邮件的用户信息列表</param>
        /// <returns>回复邮件正文</returns>
        private static string BuildReplyBody(MailReceive originalMail, List<MailUser> originalMailUsers)
        {
            var replyBuilder = new StringBuilder();

            // 按照新格式要求构建回复内容
            replyBuilder.AppendLine("<div style=\"line-height: 1.5; font-size: 8pt; color: #95a5a6;\">");
            replyBuilder.AppendLine("<div><span>Original:</span></div>");

            // 使用 MailUser 数据构建发件人信息
            var fromUsers = FormatMailAddressesForNewFormat(originalMailUsers, AddressTypeEnum.From);
            replyBuilder.AppendLine($"<div><span>&bull; From: {fromUsers}</span></div>");

            replyBuilder.AppendLine($"<div><span>&bull; Date: {originalMail.MailDate?.ToString("yyyy年MM月dd日 HH:mm:ss")} (中国 (GMT+08:00))</span></div>");

            // 使用 MailUser 数据构建收件人信息
            var toUsers = FormatMailAddressesForNewFormat(originalMailUsers, AddressTypeEnum.To);
            replyBuilder.AppendLine($"<div><span>&bull; To: {toUsers}</span></div>");

            // 使用 MailUser 数据构建抄送人信息
            var ccUsers = FormatMailAddressesForNewFormat(originalMailUsers, AddressTypeEnum.Cc);
            if (!string.IsNullOrEmpty(ccUsers))
            {
                replyBuilder.AppendLine($"<div><span>&bull; Cc: {ccUsers}</span></div>");
            }

            replyBuilder.AppendLine($"<div><span>&bull; Subject: {originalMail.MailSubject}</span></div>");
            replyBuilder.AppendLine("<div><span></span></div>");
            replyBuilder.AppendLine("</div>");

            // 添加原邮件正文
            if (!string.IsNullOrEmpty(originalMail.MailHtmlBody))
            {
                replyBuilder.AppendLine(originalMail.MailHtmlBody);
            }

            return replyBuilder.ToString();
        }

        /// <summary>
        /// 构建转发邮件正文
        /// </summary>
        /// <param name="originalMail">原邮件信息</param>
        /// <param name="originalMailUsers">原邮件的用户信息列表</param>
        /// <returns>转发邮件正文</returns>
        private static string BuildForwardBody(MailReceive originalMail, List<MailUser> originalMailUsers)
        {
            var forwardBuilder = new StringBuilder();

            // 按照新格式要求构建转发内容
            forwardBuilder.AppendLine("<div style=\"line-height: 1.5; font-size: 8pt; color: #95a5a6;\">");
            forwardBuilder.AppendLine("<div><span>Forwarded message:</span></div>");

            // 使用 MailUser 数据构建发件人信息
            var fromUsers = FormatMailAddressesForNewFormat(originalMailUsers, AddressTypeEnum.From);
            forwardBuilder.AppendLine($"<div><span>&bull; From: {fromUsers}</span></div>");

            forwardBuilder.AppendLine($"<div><span>&bull; Date: {originalMail.MailDate?.ToString("yyyy年MM月dd日 HH:mm:ss")} (中国 (GMT+08:00))</span></div>");

            // 使用 MailUser 数据构建收件人信息
            var toUsers = FormatMailAddressesForNewFormat(originalMailUsers, AddressTypeEnum.To);
            forwardBuilder.AppendLine($"<div><span>&bull; To: {toUsers}</span></div>");

            // 使用 MailUser 数据构建抄送人信息
            var ccUsers = FormatMailAddressesForNewFormat(originalMailUsers, AddressTypeEnum.Cc);
            if (!string.IsNullOrEmpty(ccUsers))
            {
                forwardBuilder.AppendLine($"<div><span>&bull; Cc: {ccUsers}</span></div>");
            }

            forwardBuilder.AppendLine($"<div><span>&bull; Subject: {originalMail.MailSubject}</span></div>");
            forwardBuilder.AppendLine("<div><span></span></div>");
            forwardBuilder.AppendLine("</div>");

            // 添加原邮件正文
            if (!string.IsNullOrEmpty(originalMail.MailHtmlBody))
            {
                forwardBuilder.AppendLine(originalMail.MailHtmlBody);
            }

            return forwardBuilder.ToString();
        }

        /// <summary>
        /// 按优先级选择回复邮件的发件邮箱
        /// 优先级：当前收件的收件邮箱 > 当前用户的个人邮箱 > 首个可选邮箱
        /// </summary>
        /// <param name="originalMail">原邮件信息</param>
        /// <param name="currentUserId">当前用户ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>选中的邮箱配置</returns>
        private async Task<MailHost?> SelectReplyMailHost(MailReceive originalMail, string currentUserId, CancellationToken cancellationToken)
        {
            // 1. 优先使用当前收件的收件邮箱（原邮件的HostId），但需要检查发送权限
            var originalMailHost = await mailHostRepository.Where(h => h.HostId == originalMail.HostId)
                .FirstAsync(cancellationToken);
            var userAccess = await mediator.Send(new GetUserAccessQuery(currentUserId, HostAccessModeEnum.Write), cancellationToken);

            if (originalMailHost != null && originalMailHost.IsEnabled && userAccess.HostId.Contains(originalMailHost.HostId))
            {
                return originalMailHost;
            }

            // 2. 获取当前用户有发送权限的所有邮箱配置
            var userAccessibleHosts = await mailHostRepository.Where(h => h.IsEnabled && userAccess.HostId.Contains(h.HostId))
                .ToListAsync(cancellationToken);

            if (userAccessibleHosts.Count > 0)
            {
                // 3. 优先查找用户的个人邮箱（IsPrivate = true 且 PrivateUserId = currentUserId）
                var personalMailHost = userAccessibleHosts.FirstOrDefault(h => h.IsPrivate && h.PrivateUserId == currentUserId);

                if (personalMailHost != null)
                {
                    return personalMailHost;
                }

                // 4. 使用首个可选邮箱
                var firstAvailableHost = userAccessibleHosts.First();
                logger.LogInformation("使用首个可选邮箱作为回复发件人: {HostId}", firstAvailableHost.HostId);
                return firstAvailableHost;
            }

            logger.LogWarning("未找到任何可用的邮箱配置用于回复邮件");
            return null;
        }

        /// <summary>
        /// 构建回复邮件的收件人和抄送人信息
        /// </summary>
        /// <param name="originalMailUsers">原邮件的用户信息列表</param>
        /// <param name="replyAll">是否回复全部</param>
        /// <returns>收件人和抄送人列表</returns>
        private static (List<MailAddressList> mailTo, List<MailAddressList>? mailCc) BuildReplyRecipients(
            List<MailUser> originalMailUsers, bool replyAll)
        {
            // 收件人：始终是原邮件的发件人
            var mailTo = originalMailUsers
                .Where(it => it.AddressType == AddressTypeEnum.From)
                .Select(it => new MailAddressList
                {
                    MailAddress = it.MailAddress,
                    DisplayName = it.DisplayName
                })
                .ToList();

            List<MailAddressList>? mailCc = null;

            if (replyAll)
            {
                // 回复全部时，将原邮件的所有收件人和抄送人添加到抄送中（除了发件人）
                var originalFromAddresses = originalMailUsers
                    .Where(it => it.AddressType == AddressTypeEnum.From)
                    .Select(it => it.MailAddress)
                    .ToHashSet();

                mailCc = originalMailUsers
                    .Where(it => it.AddressType == AddressTypeEnum.To || it.AddressType == AddressTypeEnum.Cc)
                    .Where(it => !originalFromAddresses.Contains(it.MailAddress)) // 排除发件人
                    .Select(it => new MailAddressList
                    {
                        MailAddress = it.MailAddress,
                        DisplayName = it.DisplayName
                    })
                    .GroupBy(it => it.MailAddress) // 去重
                    .Select(g => g.First())
                    .ToList();

                // 如果没有需要抄送的人，则设置为null
                if (mailCc.Count == 0)
                {
                    mailCc = null;
                }
            }

            return (mailTo, mailCc);
        }

        /// <summary>
        /// 复制原邮件的附件到新邮件
        /// </summary>
        /// <param name="originalMailId">原邮件ID</param>
        /// <param name="newMailId">新邮件ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        private async Task CopyMailAttachments(string originalMailId, string newMailId, CancellationToken cancellationToken)
        {
            // 1. 获取原邮件的所有附件
            var originalAttachments = await mailAttachmentsRepository
                .Where(a => a.MailId == originalMailId)
                .ToListAsync(cancellationToken);

            if (originalAttachments.Count == 0)
            {
                return;
            }

            // 2. 为每个附件创建新的记录，绑定到新邮件ID
            var newAttachments = new List<MailAttachments>();
            foreach (var originalAttachment in originalAttachments)
            {
                var newAttachment = new MailAttachments
                {
                    AttachmentId = Guid.NewGuid().ToString(), // 生成新的附件ID
                    Bucket = originalAttachment.Bucket,
                    Extension = originalAttachment.Extension,
                    FileName = originalAttachment.FileName, // 保持相同的文件名，因为文件内容相同
                    FileSize = originalAttachment.FileSize,
                    FileType = "2", // 2表示发件附件
                    InputTime = DateTime.Now,
                    MailId = newMailId, // 绑定到新邮件ID
                    RealName = originalAttachment.RealName,
                    ServerPath = originalAttachment.ServerPath // 保持相同的服务器路径，因为文件内容相同
                };

                newAttachments.Add(newAttachment);
            }

            // 3. 批量插入新的附件记录
            await mailAttachmentsRepository.InsertAsync(newAttachments, cancellationToken);
        }

        /// <summary>
        /// 复制原邮件的附件到转发邮件（根据附件大小决定附件类型）
        /// </summary>
        /// <param name="originalMailId">原邮件ID</param>
        /// <param name="newMailId">新邮件ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        private async Task CopyMailAttachmentsForForward(string originalMailId, string newMailId, CancellationToken cancellationToken)
        {
            // 1. 获取原邮件的所有附件
            var originalAttachments = await mailAttachmentsRepository
                .Where(a => a.MailId == originalMailId)
                .ToListAsync(cancellationToken);

            if (originalAttachments.Count == 0)
            {
                return;
            }

            // 2. 计算附件总大小（50MB = 50 * 1024 * 1024 bytes）
            const long maxSizeForNormalAttachment = 50L * 1024 * 1024;
            var totalSize = originalAttachments.Sum(a => a.FileSize ?? 0);
            var attachmentType = totalSize <= maxSizeForNormalAttachment ? "2" : "3"; // 2=普通附件, 3=云附件

            // 3. 为每个附件创建新的记录，绑定到新邮件ID
            var newAttachments = new List<MailAttachments>();
            foreach (var originalAttachment in originalAttachments)
            {
                var newAttachment = new MailAttachments
                {
                    AttachmentId = Guid.NewGuid().ToString(), // 生成新的附件ID
                    Bucket = originalAttachment.Bucket,
                    Extension = originalAttachment.Extension,
                    FileName = originalAttachment.FileName, // 保持相同的文件名，因为文件内容相同
                    FileSize = originalAttachment.FileSize,
                    FileType = attachmentType, // 根据总大小决定附件类型
                    InputTime = DateTime.Now,
                    MailId = newMailId, // 绑定到新邮件ID
                    RealName = originalAttachment.RealName,
                    ServerPath = originalAttachment.ServerPath // 保持相同的服务器路径，因为文件内容相同
                };

                newAttachments.Add(newAttachment);
            }

            // 4. 批量插入新的附件记录
            await mailAttachmentsRepository.InsertAsync(newAttachments, cancellationToken);
        }

        /// <summary>
        /// 获取用户签名（如果用户只有一个签名则自动添加）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>签名内容，如果用户没有签名或有多个签名则返回null</returns>
        private async Task<string?> GetUserSignatureIfSingle(string userId, CancellationToken cancellationToken)
        {
            // 查询用户的所有签名
            var userSignatures = await mailSignatureRepository
                .Where(s => s.UserId == userId)
                .ToListAsync(cancellationToken);

            // 如果用户只有一个签名，则返回该签名的内容
            if (userSignatures.Count == 1)
            {
                var signature = userSignatures.First();
                return signature.Content;
            }

            return null;
        }

        /// <summary>
        /// 格式化邮箱地址列表为新格式的 HTML 字符串
        /// </summary>
        /// <param name="mailUsers">邮件用户列表</param>
        /// <param name="addressType">地址类型</param>
        /// <returns>格式化后的邮箱地址 HTML 字符串</returns>
        private static string FormatMailAddressesForNewFormat(List<MailUser> mailUsers, string addressType)
        {
            var addresses = mailUsers
                .Where(u => u.AddressType == addressType)
                .Select(u => FormatSingleMailAddressForNewFormat(u.MailAddress, u.DisplayName))
                .ToList();

            return string.Join("; ", addresses);
        }

        /// <summary>
        /// 格式化单个邮箱地址为新格式的 HTML 格式
        /// </summary>
        /// <param name="mailAddress">邮箱地址</param>
        /// <param name="displayName">显示名称</param>
        /// <returns>格式化后的 HTML 邮箱地址</returns>
        private static string FormatSingleMailAddressForNewFormat(string mailAddress, string displayName)
        {
            if (string.IsNullOrEmpty(mailAddress))
            {
                return string.Empty;
            }

            // 新格式：显示名称<span style="color: #089cff;"><a href="mailto:邮箱地址" target="_blank" rel="noopener" style="color: #089cff;">&lt;邮箱地址&gt;</a></span>
            var userName = string.IsNullOrEmpty(displayName) ? mailAddress : displayName;
            return $"{userName}<span style=\"color: #089cff;\"><a href=\"mailto:{mailAddress}\" target=\"_blank\" rel=\"noopener\" style=\"color: #089cff;text-decoration: none;\">&lt;{mailAddress}&gt;</a></span>";
        }

        /// <summary>
        /// 创建邮件关联关系
        /// </summary>
        /// <param name="receiveMailId">收件邮件ID</param>
        /// <param name="sendMailId">发件邮件ID</param>
        /// <param name="currentUserId">当前用户ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        private async Task CreateMailCorrelationAsync(string receiveMailId, string sendMailId, string currentUserId, CancellationToken cancellationToken)
        {
            var currentTime = DateTime.Now;

            var correlations = new List<MailCorrelative>
            {
                // 发件邮件关联收件邮件
                new MailCorrelative
                {
                    Id = Guid.NewGuid().ToString(),
                    MailId = sendMailId,
                    CorrelateType = SysEnum.CorrelateType.ReceiveMail.ToString(),
                    ObjId = receiveMailId,
                    CreateBy = currentUserId,
                    CreateTime = currentTime
                }
            };

            await mailCorrelativeRepository.InsertAsync(correlations, cancellationToken);

        }

    }
}
