﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenter.Applications.Models.Count;
using iPlatformExtension.MailCenter.Applications.Queries.Count;
using iPlatformExtension.MailCenter.HostedService;
using iPlatformExtension.Model.MailCenter;
using MediatR;
using Microsoft.Extensions.Logging;
using static iPlatformExtension.Model.Enum.SysEnum;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Count
{
    public class GetScheduledSendCountHandler(
         ILogger<InitMailCountService> logger,
        IFreeSql<MailCenterFreeSql> freeSql,
        IRedisCache<RedisCacheOptionsBase> redisCache,
        IFreeSql<PlatformFreeSql> platformFreeSql
        ) : IRequestHandler<GetScheduledSendCountQuery, Dictionary<string, CacheValueModel>>
    {
        public async Task<Dictionary<string, CacheValueModel>> Handle(GetScheduledSendCountQuery request, CancellationToken cancellationToken)
        {
            try
            {
                var defaultDict = request.defaultDict;
                var cacheTime = request._cacheTime ?? TimeSpan.FromMinutes(5);
                logger.LogInformation("开始统计定时发送数量");
                var count = await freeSql
                    .Select<MailSend, MailSendFlow>()
                    .LeftJoin(o => o.t1.MailId == o.t2.MailId)
                    .Where(o =>
                        o.t1.Status == SendStatusType.ScheduledSend.GetHashCode()
                    )
                    .WithLock()
                    .GroupBy(o => o.t2.UndertakeUserId)
                    .ToDictionaryAsync(
                        it => new CacheValueModel { Count = it.Count(), CreateTime = DateTime.Now },
                        cancellationToken
                    );

                // 将查询结果合并到默认字典中
                foreach (var item in count)
                {
                    defaultDict[item.Key] = item.Value;
                }

                await redisCache.RemoveCacheKeyAsync("ScheduledSendCount", cancellationToken);
                await redisCache.SetCacheValuesAsync(
                    "ScheduledSendCount",
                    defaultDict,
                    cacheTime,
                    cancellationToken: cancellationToken
                );

                logger.LogInformation("定时发送数量统计统计完成，共{UserCount}个", count.Count);
                return defaultDict;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "定时发送数量统计统异常");
                return request.defaultDict;
            }
        }
    }
}
