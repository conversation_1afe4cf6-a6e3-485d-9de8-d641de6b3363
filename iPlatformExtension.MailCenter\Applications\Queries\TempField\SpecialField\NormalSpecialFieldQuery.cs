using iPlatformExtension.MailCenter.Applications.Models.Template;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Queries.TempField.SpecialField;

/// <summary>
/// 普通特殊字段查询
/// </summary>
/// <param name="Field">字段配置</param>
/// <param name="DataGroups">邮件数据组列表</param>
public record NormalSpecialFieldQuery(
    Model.MailCenter.TempField Field,
    List<MailDataGroup>? DataGroups = null
) : ISpecialFieldQuery, IRequest<Dictionary<string, string>>
{
    List<MailDataGroup> ISpecialFieldQuery.DataGroups =>
        DataGroups ?? [];
}
