using FreeSql;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenter.Applications.Models.Template;
using iPlatformExtension.MailCenter.Applications.Queries.TempField.SpecialField;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;
using iPlatformExtension.Repository.Interface;
using Microsoft.Extensions.Logging;

namespace iPlatformExtension.MailCenter.Applications.Handlers.TempField.SpecialField;

/// <summary>
/// task_fee特殊字段查询处理器
/// </summary>
internal sealed class TaskFeeSpecialFieldQueryHandler(
    IFreeSql<PlatformFreeSql> freeSql,
    ICustomerRepository customerRepository,
    IApplyTypeRepository applyTypeRepository,
    IBaseCaseStatusRepository baseCaseStatusRepository,
    IBaseCtrlProcRepository baseCtrlProcRepository,
    ILogger<TaskFeeSpecialFieldQueryHandler> logger)
    : BaseSpecialFieldQueryHandler<TaskFeeSpecialFieldQuery>(freeSql, customerRepository, applyTypeRepository, baseCaseStatusRepository, baseCtrlProcRepository)
{
    public override async Task<Dictionary<string, string>> Handle(TaskFeeSpecialFieldQuery request, CancellationToken cancellationToken)
    {
        var result = new Dictionary<string, string>();
        List<string>? procIds = null;

        try
        {
            // 从DataGroups中提取ProcId列表，保持顺序不去重，确保多行数据的对应关系
            procIds = request.DataGroups?
                .Where(x => !string.IsNullOrEmpty(x.ProcId))
                .Select(x => x.ProcId!)
                .ToList(); // 移除.Distinct()，保持多行数据的完整性

            if (procIds == null || procIds.Count == 0)
            {
                return result;
            }

            var fieldKey = request.Field.Key?.ToLower() ?? string.Empty;

            // 判断是否为列表类型字段
            if (fieldKey.Contains("list_task_fee") || fieldKey.Contains("list_total_fee"))
            {
                // 多行列表处理
                var listResult = await QueryTaskFeeListForMultipleRowsIndexed(procIds, request.Field, cancellationToken);
                foreach (var kvp in listResult)
                {
                    result[kvp.Key] = kvp.Value;
                }
            }
            else
            {
                // 单值处理
                var isOfficialFeeOnly = !fieldKey.Contains("total_fee");
                var totalAmount = await QueryFeeAmount(procIds, isOfficialFeeOnly, cancellationToken);

                if (totalAmount > 0)
                {
                    // 处理Logo标记的字段值
                    var fieldValue = await ProcessFieldValueWithLogoAsync(totalAmount, request.Field, cancellationToken);

                    if (!string.IsNullOrEmpty(fieldValue))
                    {
                        result[request.Field.Key!] = fieldValue;
                    }
                }
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "TaskFeeSpecialFieldQueryHandler处理发生异常. FieldKey: {FieldKey}, ProcIds: {ProcIds}",
                request.Field.Key, string.Join(",", procIds ?? []));
        }

        return result;
    }

    /// <summary>
    /// 查询费用数据 - 统计指定ProcId列表中费用的总和
    /// </summary>
    /// <param name="procIds">任务ID列表</param>
    /// <param name="isOfficialFeeOnly">是否只查询官费，true=只查询官费，false=查询所有费用</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>费用总和</returns>
    private async Task<decimal> QueryFeeAmount(List<string> procIds, bool isOfficialFeeOnly, CancellationToken cancellationToken)
    {
        var query = freeSql.Select<CaseFeeList>()
            .Where(fee => procIds.Contains(fee.ProcId) && fee.IsEnabled == true);

        if (isOfficialFeeOnly)
        {
            query = query.Where(fee => fee.FeeClass == FeeClass.Official);
        }

        var totalAmount = await query.SumAsync(fee => fee.Amount ?? 0, cancellationToken);
        return totalAmount;
    }

    /// <summary>
    /// 查询task_fee列表数据 - 支持多行数据，生成索引化的键值对，用于列表类型字段
    /// 为每个procId分别进行统计
    /// </summary>
    private async Task<Dictionary<string, string>> QueryTaskFeeListForMultipleRowsIndexed(
        List<string> procIds,
        Model.MailCenter.TempField field,
        CancellationToken cancellationToken)
    {
        var result = new Dictionary<string, string>();

        try
        {
            // 按照procIds的顺序逐个处理，确保数据行与ProcId的对应关系正确
            for (int i = 0; i < procIds.Count; i++)
            {
                var procId = procIds[i];

                var fieldKey = field.Key?.ToLower() ?? string.Empty;
                var isOfficialFeeOnly = !fieldKey.Contains("list_total_fee");
                var totalAmount = await QueryFeeAmount([procId], isOfficialFeeOnly, cancellationToken);

                // 处理Logo标记的字段值（即使金额为0也要处理，保持数据行的完整性）
                var fieldValue = await ProcessFieldValueWithLogoAsync(totalAmount, field, cancellationToken);

                // 生成索引化的键：fieldKey_index，确保与传入的procIds顺序一致
                var indexedKey = $"{field.Key}_{i}";
                result[indexedKey] = fieldValue;
            }

            // 生成总行数信息，与传入的procIds数量一致
            result["_rowCount"] = procIds.Count.ToString();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "QueryTaskFeeListForMultipleRowsIndexed查询task_fee列表数据时发生异常. FieldKey: {FieldKey}, ProcIds: {ProcIds}",
                field.Key, string.Join(",", procIds));
        }

        return result;
    }
}
