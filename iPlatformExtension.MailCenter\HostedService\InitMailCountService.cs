﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenter.Applications.Models.Count;
using iPlatformExtension.MailCenter.Applications.Queries.Count;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using MediatR;
using MongoDB.Driver;
using static iPlatformExtension.Model.Enum.SysEnum;

namespace iPlatformExtension.MailCenter.HostedService
{
    /// <summary>
    /// 初始化邮件数量统计服务
    /// </summary>
    public class InitMailCountService(
        ILogger<InitMailCountService> logger,
        IFreeSql<MailCenterFreeSql> freeSql,
        IRedisCache<RedisCacheOptionsBase> redisCache,
        IFreeSql<PlatformFreeSql> platformFreeSql,
        IMediator mediator) : BackgroundService
    {
        private readonly ILogger<InitMailCountService> _logger = logger;
        private readonly IFreeSql<MailCenterFreeSql> _freeSql = freeSql;
        private readonly IFreeSql<PlatformFreeSql> _platformFreeSql = platformFreeSql;
        private readonly IRedisCache<RedisCacheOptionsBase> _redisCache = redisCache;
        private readonly IMediator _mediator = mediator;
        private readonly TimeSpan _cacheTime = TimeSpan.FromMinutes(15);

        /// <inheritdoc />
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("初始化邮件数量统计服务启动");

            // 第一次统计
            await RunStatistics(stoppingToken);

            // 每10分钟执行一次统计
            using var timer = new PeriodicTimer(TimeSpan.FromMinutes(10));

            try
            {
                while (await timer.WaitForNextTickAsync(stoppingToken))
                {
                    _logger.LogInformation("定时统计邮件数量开始");
                    await RunStatistics(stoppingToken);
                    _logger.LogInformation("定时统计邮件数量完成");
                }
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("邮件数量统计服务已停止");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "定时统计邮件数量异常");
            }
        }

        /// <summary>
        /// 执行统计操作
        /// </summary>
        private async Task RunStatistics(CancellationToken stoppingToken)
        {
            try
            {
                // 获取所有用户ID并创建默认值字典
                var userIds = await GetAllUserIds(stoppingToken);
                var defaultHandleDict = CreateDefaultDictionary(userIds);
                var defaultReaderDict = CreateDefaultDictionary(userIds);

                // 统计Handle数量
                await GetHandleCount(defaultHandleDict, stoppingToken);

                // 统计Allot数量
                await GetAllotCount(stoppingToken);

                // 统计ReaderList数量
                await GetReaderListCount(defaultReaderDict, stoppingToken);

                //await GetScheduledSendCount(defaultReaderDict, stoppingToken);
                
                await _mediator.Send(new GetScheduledSendCountQuery(defaultReaderDict, _cacheTime));
                await _mediator.Send(new GetErrorSendCountQuery(defaultReaderDict, _cacheTime));
                await _mediator.Send(new GetAuditSendMailCountQuery(defaultReaderDict, _cacheTime));
                await _mediator.Send(new GetSendReaderListCountQuery(defaultReaderDict, _cacheTime));

                _logger.LogInformation("邮件数量统计完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "邮件数量统计异常");
            }
        }

        /// <summary>
        /// 获取所有用户ID
        /// </summary>
        private async Task<List<string>> GetAllUserIds(CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("开始获取所有用户ID");
                var userIds = await _platformFreeSql
                    .Select<SysUserInfo>()
                    .Where(u => u.IsEnabled)
                    .ToListAsync(u => u.UserId, cancellationToken);

                _logger.LogInformation("获取所有用户ID完成，共{Count}个用户", userIds.Count);
                return userIds;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有用户ID异常");
                return [];
            }
        }

        /// <summary>
        /// 创建默认值字典
        /// </summary>
        private static Dictionary<string, CacheValueModel> CreateDefaultDictionary(List<string> userIds)
        {
            var defaultDict = new Dictionary<string, CacheValueModel>();
            foreach (var userId in userIds)
            {
                defaultDict[userId] = new CacheValueModel { Count = 0, CreateTime = DateTime.Now };
            }
            return defaultDict;
        }

        /// <summary>
        /// 获取待分拣邮件数量
        /// </summary>
        private async Task<Dictionary<string, CacheValueModel>> GetAllotCount(
            CancellationToken cancellationToken
        )
        {
            try
            {
                _logger.LogInformation("开始统计待分拣邮件数量");

                // 获取所有邮箱ID并创建默认值字典
                var mailHosts = await _freeSql
                    .Select<Model.MailCenter.MailHost>()
                    .Where(h => h.IsEnabled)
                    .ToListAsync(h => h.HostId, cancellationToken);

                _logger.LogInformation("获取到{MailCount}个启用状态的邮箱", mailHosts.Count);

                // 创建默认值字典
                var defaultDict = new Dictionary<string, CacheValueModel>();
                foreach (var hostId in mailHosts)
                {
                    defaultDict[hostId] = new CacheValueModel { Count = 0, CreateTime = DateTime.Now };
                }

                // 查询待分拣邮件数量
                var count = await _freeSql
                    .Select<MailReceive>()
                    .Where(it => it.Status == 2) // 待分拣状态
                    .WithLock()
                    .GroupBy(it => it.HostId)
                    .ToDictionaryAsync(
                        it => new CacheValueModel { Count = it.Count(), CreateTime = DateTime.Now },
                        cancellationToken
                    );

                // 将查询结果合并到默认字典中
                foreach (var item in count)
                {
                    defaultDict[item.Key] = item.Value;
                }

                await _redisCache.RemoveCacheKeyAsync("Allot", cancellationToken);
                await _redisCache.SetCacheValuesAsync(
                    "Allot",
                    defaultDict,
                    _cacheTime,
                    cancellationToken: cancellationToken
                );

                _logger.LogInformation("待分拣邮件数量统计完成，共{MailCount}个邮箱有待分拣邮件", count.Count);
                return defaultDict;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "统计待分拣邮件数量异常");
                return [];
            }
        }

        /// <summary>
        /// 获取待办理邮件数量
        /// </summary>
        private async Task<Dictionary<string, CacheValueModel>> GetHandleCount(
            Dictionary<string, CacheValueModel> defaultDict,
            CancellationToken cancellationToken
        )
        {
            try
            {
                _logger.LogInformation("开始统计待办理邮件数量");
                // 获取审核用户和对应的数量统计
                var count = await _freeSql
                    .Select<MailReceive, FlowRecord>()
                    .InnerJoin(it =>
                        it.t1.MailId == it.t2.MailId
                        && it.t2.IsCurrent == SysEnum.Status.Enable.GetHashCode()
                    )
                    .Where(it => it.t2 != null)
                    .WithLock()
                    .GroupBy(it => it.t2.AuditUser)
                    .ToDictionaryAsync(
                        it => new CacheValueModel { Count = it.Count(), CreateTime = DateTime.Now },
                        cancellationToken
                    );

                // 将查询结果合并到默认字典中
                foreach (var item in count)
                {
                    defaultDict[item.Key] = item.Value;
                }

                await _redisCache.RemoveCacheKeyAsync("Handle", cancellationToken);
                await _redisCache.SetCacheValuesAsync(
                    "Handle",
                    defaultDict,
                    _cacheTime,
                    cancellationToken: cancellationToken
                );

                _logger.LogInformation("待办理邮件数量统计完成，共{UserCount}个用户有待办理邮件", count.Count);
                return defaultDict;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "统计待办理邮件数量异常");
                return defaultDict;
            }
        }

        /// <summary>
        /// 获取待阅读邮件数量
        /// </summary>
        private async Task<Dictionary<string, CacheValueModel>> GetReaderListCount(
            Dictionary<string, CacheValueModel> defaultDict,
            CancellationToken cancellationToken
        )
        {
            try
            {
                _logger.LogInformation("开始统计待阅读邮件数量");
                var count = await _freeSql
                    .Select<MailReaderList, MailReceive>()
                    .LeftJoin(o => o.t1.MailId == o.t2.MailId)
                    .Where(o =>
                        o.t1.Status == (int)ReaderStatusEnum.ToRead
                        && o.t2.Status >= SysEnum.ReceiveFileType.Handle.GetHashCode()
                    )
                    .WithLock()
                    .GroupBy(o => o.t1.UserId)
                    .ToDictionaryAsync(
                        it => new CacheValueModel { Count = it.Count(), CreateTime = DateTime.Now },
                        cancellationToken
                    );

                // 将查询结果合并到默认字典中
                foreach (var item in count)
                {
                    defaultDict[item.Key] = item.Value;
                }

                await _redisCache.RemoveCacheKeyAsync("ReaderList", cancellationToken);
                await _redisCache.SetCacheValuesAsync(
                    "ReaderList",
                    defaultDict,
                    _cacheTime,
                    cancellationToken: cancellationToken
                );

                _logger.LogInformation("待阅读邮件数量统计完成，共{UserCount}个用户有待阅读邮件", count.Count);
                return defaultDict;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "统计待阅读邮件数量异常");
                return defaultDict;
            }
        }
    }
}
