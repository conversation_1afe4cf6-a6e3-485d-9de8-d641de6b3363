﻿using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.MailCenter.Applications.Models.Receive;
using iPlatformExtension.MailCenter.Applications.Queries.Receive;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.Repository.Interface;
using MediatR;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Queries.SysConfig;
using iPlatformExtension.MailCenterRepository.Interface;
using Microsoft.AspNetCore.Components.Forms;
using iPlatformExtension.Model.BaseModel;
using System.Collections.Generic;
using iPlatformExtension.Model.Enum;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Receive
{
    /// <summary>
    /// 查询收件信息
    /// </summary>
    internal sealed class GetReceiveListQueryHandler(IFreeSql<MailCenterFreeSql> freeSql, IUserInfoRepository userInfoRepository,
        IHttpContextAccessor content, IMediator mediator, IMailHostRepository mailHostRepository, IFreeSql<PlatformFreeSql> platformFreesql) : IRequestHandler<GetReceiveListQuery, IEnumerable<GetReceiveListDto>>
    {
        public async Task<IEnumerable<GetReceiveListDto>> Handle(GetReceiveListQuery request, CancellationToken cancellationToken)
        {
            var userId = content.HttpContext?.User.GetUserID() ?? string.Empty;
            var mailNoList = request.MailNo?.Replace(" ", ";").Split(";");
            var receiveMailList = request.ReceiveMail?.Replace(" ", ";").Split(";");

            var mailReceiveListSelect = freeSql.Select<MailReceive, MailReceiveFlow, MailReaderList>()
                .LeftJoin(it => it.t1.MailId == it.t2.MailId)
                .LeftJoin(it => it.t1.MailId == it.t3.MailId).WithLock()
                .WhereIf(request.IgnoreBy is not null && request.IgnoreBy.Count > 0, it => request.IgnoreBy!.Contains(it.t2.IgnoreBy))
                .WhereIf(request.MailFrom is not null, it => it.t1.MailFrom.Contains(request.MailFrom))
                .WhereIf(request.ReceiveMail is not null, it => receiveMailList.Contains(it.t1.HostId))
                .WhereIf(request.Status != null && request.Status.Any(), it => request.Status.Contains((int)it.t1.Status))
                .WhereIf(request.UndertakeUserId is not null && request.UndertakeUserId.Count > 0, it => request.UndertakeUserId!.Contains(it.t2.UndertakeUserId))
                .WhereIf(request.SortBy is not null && request.SortBy.Count > 0, it => request.SortBy!.Contains(it.t2.SortBy))
                .WhereIf(request.IgnoreTimeStart is not null, it => it.t2.IgnoreTime!.Value.Date >= request.IgnoreTimeStart!.Value.Date)
                .WhereIf(request.IgnoreTimeEnd is not null, it => it.t2.IgnoreTime!.Value.Date <= request.IgnoreTimeEnd!.Value.Date)
                .WhereIf(request.FinishDateStart is not null, it => it.t2.FinishDate!.Value.Date >= request.FinishDateStart!.Value.Date)
                .WhereIf(request.FinishDateEnd is not null, it => it.t2.FinishDate!.Value.Date <= request.FinishDateEnd!.Value.Date)
                .WhereIf(request.SortTimeStart is not null, it => it.t2.SortTime!.Value.Date >= request.SortTimeStart!.Value.Date)
                .WhereIf(request.SortTimeEnd is not null, it => it.t2.SortTime!.Value.Date <= request.SortTimeEnd!.Value.Date)
                .WhereIf(request.SendDateStart is not null, it => it.t1.MailDate!.Value.Date >= request.SendDateStart!.Value.Date)
                .WhereIf(request.SendDateEnd is not null, it => it.t1.MailDate!.Value.Date <= request.SendDateEnd!.Value.Date)
                .WhereIf(request.MailSubject is not null, it => it.t1.MailSubject.Contains(request.MailSubject))
                .WhereIf(request.MailNo is not null, it => mailNoList.Contains(it.t1.MailNo))
                .WhereIf(request.Search is not null, it => it.t1.MailSubject.Contains(request.Search) || it.t1.MailFrom.Contains(request.Search) || it.t1.MailNo == request.Search)
                .WhereIf(request.ReadUser is not null && request.ReadUser.Count > 0, it => request.ReadUser.Contains(it.t3.UserId));
            //排序
            mailReceiveListSelect = string.Equals(request.SortType, "asc", comparisonType: StringComparison.OrdinalIgnoreCase) ?
                mailReceiveListSelect.OrderByPropertyName(request.Sort) : mailReceiveListSelect.OrderByPropertyName(request.Sort, false);
            var hostIds = await mediator.Send(new GetUserAccessQuery(userId, request.Ignore ? "sorter" : null), cancellationToken);
            if (!request.Ignore)
            {
                hostIds.HostId.AddRange((await mediator.Send(new GetUserAccessQuery(userId, request.Ignore ? "read" : null), cancellationToken)).HostId);
            }
            //获取用户角色
            var mailSuperManager = await platformFreesql.Select<SysUserRole, SysRoleInfo>()
                .LeftJoin(it => it.t1.RoleId == it.t2.RoleId)
                .Where(it => it.t1.UserId == userId && it.t2.RoleCode == "MailSuperManager")
                .ToListAsync(cancellationToken);
            //如果用户是超级管理员，则不进行权限过滤
            if (mailSuperManager.Any())
            {
                mailReceiveListSelect = request.Ignore ? mailReceiveListSelect.Where(it => hostIds.HostId.Contains(it.t1.HostId)) : mailReceiveListSelect;
            }
            else
            {
                mailReceiveListSelect = request.Ignore ? mailReceiveListSelect.Where(it => hostIds.HostId.Contains(it.t1.HostId)) :
                    mailReceiveListSelect.Where(it => hostIds.HostId.Contains(it.t1.HostId)
                        || it.t2.UndertakeUserId == userId || it.t2.IgnoreBy == userId || it.t2.SortBy == userId || it.t3.UserId.Contains(userId));
            }

            var count = await mailReceiveListSelect.GroupBy(it => it.t1.MailId).CountAsync(cancellationToken);
            var getReceiveListDtos = await mailReceiveListSelect.Distinct()
                .Page(request.PageIndex!.Value, request.PageSize!.Value).ToListAsync(it => new GetReceiveListDto(it.t1.MailFrom,
                    it.t1.MailSubject, it.t1.MailDate, it.t2.FinishDate, it.t2.IgnoreTime,
                    it.t2.IgnoreBy, it.t2.SortBy, it.t2.SortTime, it.t1.Status, it.t2.UndertakeUserId, it.t1.MailNo, it.t1.MailId, it.t1.MailPriority, it.t1.HostId, it.t1.MailTo, it.t1.CreateTime),
                cancellationToken);
            return new PageResult<GetReceiveListDto>()
            {
                Data = await getReceiveListDtos.ToAsyncEnumerable().SelectAwait(async receiveList =>
                {
                    if (receiveList.UndertakeUserTemp is not null)
                    {
                        ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository = userInfoRepository;
                        receiveList.UndertakeUser = new { CnName = (await userBaseInfoRepository.GetCacheValueAsync(receiveList.UndertakeUserTemp, cancellationToken: cancellationToken))?.CnName ?? "", UserId = receiveList.UndertakeUserTemp };
                    }

                    if (receiveList.IgnoreByTemp is not null)
                    {
                        ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository = userInfoRepository;
                        receiveList.IgnoreBy = new { CnName = (await userBaseInfoRepository.GetCacheValueAsync(receiveList.IgnoreByTemp, cancellationToken: cancellationToken))?.CnName ?? "", UserId = receiveList.IgnoreByTemp };
                    }

                    if (receiveList.SortByTemp is not null)
                    {
                        ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository = userInfoRepository;
                        receiveList.SortBy = new { CnName = (await userBaseInfoRepository.GetCacheValueAsync(receiveList.SortByTemp, cancellationToken: cancellationToken))?.CnName ?? "", UserId = receiveList.SortByTemp };
                    }

                    if (!string.IsNullOrWhiteSpace(receiveList.HostId))
                    {
                        var cacheValueAsync = await mailHostRepository.GetCacheValueAsync(receiveList.HostId, cancellationToken: cancellationToken);
                        receiveList.IsPrivate = cacheValueAsync?.IsPrivate;
                    }

                    // 检查是否打上个人标签
                    receiveList.HasPersonalTag = await freeSql.Select<MailTagList, MailTag>()
                        .LeftJoin((tl, t) => tl.TagId == t.Id)
                        .Where((tl, t) => tl.MailId == receiveList.MailId && t.UserId == userId && t.MailType == SysEnum.MailType.Receive.ToString())
                        .WithLock()
                        .AnyAsync(cancellationToken);

                    return receiveList;
                }).ToListAsync(cancellationToken: cancellationToken),
                Page = request.PageIndex.Value,
                PageSize = request.PageSize.Value,
                Total = count
            };
        }
    }
}

