using iPlatformExtension.Repository.Interface;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Common.Db.FreeSQL;
using FreeSql;
using System.Globalization;

namespace iPlatformExtension.MailCenter.Applications.Extensions;

/// <summary>
/// 字段值处理扩展方法
/// </summary>
public static class FieldValueExtensions
{
    /// <summary>
    /// 处理带有Logo标记的字段值（支持多语言转换）
    /// </summary>
    /// <param name="value">原始值</param>
    /// <param name="field">字段配置</param>
    /// <param name="applyTypeRepository">申请类型仓储</param>
    /// <param name="baseCaseStatusRepository">案件状态仓储</param>
    /// <param name="customerRepository">客户仓储</param>
    /// <param name="baseCtrlProcRepository">任务仓储</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理后的值</returns>
    public static async Task<string> ProcessFieldValueWithLogoAsync(
        this object? value,
        Model.MailCenter.TempField field,
        IApplyTypeRepository applyTypeRepository,
        IBaseCaseStatusRepository baseCaseStatusRepository,
        ICustomerRepository customerRepository,
        IBaseCtrlProcRepository baseCtrlProcRepository,
        IUserInfoRepository userInfoRepository,
        CancellationToken cancellationToken = default)
    {
        if (value == null) return string.Empty;

        // 处理日期计算逻辑
        var processedValue = ProcessDateCalculation(value, field);

        // 如果没有Logo标记，直接格式化返回
        if (string.IsNullOrEmpty(field.Logo))
        {
            return FormatValue(processedValue, field.Format, field.Key);
        }

        // 根据Logo标记进行多语言转换
        var convertedValue = await ConvertValueByLogoAsync(processedValue.ToString() ?? string.Empty, field.Logo, applyTypeRepository, baseCaseStatusRepository, customerRepository, baseCtrlProcRepository, cancellationToken);

        // 对转换后的值进行格式化
        return FormatValue(convertedValue, field.Format, field.Key);
    }

    /// <summary>
    /// 根据Logo标记转换值（多语言支持）
    /// </summary>
    /// <param name="value">原始值</param>
    /// <param name="logo">Logo标记</param>
    /// <param name="applyTypeRepository">申请类型仓储</param>
    /// <param name="baseCaseStatusRepository">案件状态仓储</param>
    /// <param name="customerRepository">客户仓储</param>
    /// <param name="baseCtrlProcRepository">任务仓储</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>转换后的值</returns>
    private static async Task<string> ConvertValueByLogoAsync(
        string value,
        string logo,
        IApplyTypeRepository applyTypeRepository,
        IBaseCaseStatusRepository baseCaseStatusRepository,
        ICustomerRepository customerRepository,
        IBaseCtrlProcRepository baseCtrlProcRepository,
        CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(value) || string.IsNullOrEmpty(logo))
        {
            return value;
        }

        try
        {
            // 根据Logo标记进行不同的转换
            return logo.ToLower() switch
            {
                // 申请类型相关转换
                "apply_type_cn" => await GetApplyTypeNameAsync(value, "zh_cn", applyTypeRepository),
                "apply_type_en" => await GetApplyTypeNameAsync(value, "en_us", applyTypeRepository),

                // 案件状态相关转换
                "case_status_cn" => await GetCaseStatusNameAsync(value, "zh_cn", baseCaseStatusRepository),
                "case_status_en" => await GetCaseStatusNameAsync(value, "en_us", baseCaseStatusRepository),

                // 客户名称相关转换
                "cus_customer_cn" => await GetCustomerNameAsync(value, "zh_cn", customerRepository),
                "cus_customer_en" => await GetCustomerNameAsync(value, "en_us", customerRepository),

                // 任务名称相关转换
                "ctrl_proc_name_cn" => await GetCtrlProcNameAsync(value, "zh_cn", baseCtrlProcRepository),
                "ctrl_proc_name_en" => await GetCtrlProcNameAsync(value, "en_us", baseCtrlProcRepository),

                // "country_cn" => await GetCountryNameAsync(value, "zh_cn", countryRepository),
                // "country_en" => await GetCountryNameAsync(value, "en_us", countryRepository),

                _ => value // 未知Logo标记，返回原值
            };
        }
        catch
        {
            // 转换失败时返回原值
            return value;
        }
    }

    /// <summary>
    /// 获取申请类型名称
    /// </summary>
    /// <param name="applyTypeId">申请类型ID</param>
    /// <param name="language">语言类型（zh_cn/en_us）</param>
    /// <param name="applyTypeRepository">申请类型仓储</param>
    /// <returns>申请类型名称</returns>
    private static async Task<string> GetApplyTypeNameAsync(
        string applyTypeId,
        string language,
        IApplyTypeRepository applyTypeRepository)
    {
        var applyType = await applyTypeRepository.GetCacheValueAsync(applyTypeId);
        if (applyType == null) return applyTypeId;

        return language.ToLower() switch
        {
            "zh_cn" => applyType.ApplyTypeZhCn ?? applyTypeId,
            "en_us" => applyType.ApplyTypeEnUs ?? applyTypeId,
            _ => applyTypeId
        };
    }

    /// <summary>
    /// 获取案件状态名称
    /// </summary>
    /// <param name="statusId">状态ID</param>
    /// <param name="language">语言类型（zh_cn/en_us）</param>
    /// <param name="baseCaseStatusRepository">案件状态仓储</param>
    /// <returns>状态名称</returns>
    private static async Task<string> GetCaseStatusNameAsync(
        string statusId,
        string language,
        IBaseCaseStatusRepository baseCaseStatusRepository)
    {
        var status = await baseCaseStatusRepository.GetCacheValueAsync(statusId);
        if (status == null) return statusId;

        return language.ToLower() switch
        {
            "zh_cn" => status.CaseStatusZhCn ?? statusId,
            "en_us" => status.CaseStatusEnUs ?? statusId,
            _ => statusId
        };
    }

    /// <summary>
    /// 获取客户名称
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="language">语言类型（zh_cn/en_us）</param>
    /// <param name="customerRepository">客户仓储</param>
    /// <returns>客户名称</returns>
    private static async Task<string> GetCustomerNameAsync(
        string customerId,
        string language,
        ICustomerRepository customerRepository)
    {
        var customer = await customerRepository.GetCacheValueAsync(customerId);
        if (customer == null) return customerId;

        return language.ToLower() switch
        {
            "zh_cn" => customer.CustomerName ?? customerId,
            "en_us" => customer.CustomerNameEn ?? customerId,
            _ => customerId
        };
    }

    /// <summary>
    /// 获取任务名称
    /// </summary>
    /// <param name="ctrlProcId">任务ID</param>
    /// <param name="language">语言类型（zh_cn/en_us）</param>
    /// <param name="baseCtrlProcRepository">任务仓储</param>
    /// <returns>任务名称</returns>
    private static async Task<string> GetCtrlProcNameAsync(
        string ctrlProcId,
        string language,
        IBaseCtrlProcRepository baseCtrlProcRepository)
    {
        var ctrlProc = await baseCtrlProcRepository.GetCacheValueAsync(ctrlProcId);
        if (ctrlProc == null) return ctrlProcId;

        return language.ToLower() switch
        {
            "zh_cn" => ctrlProc.CtrlProcZhCn ?? ctrlProcId,
            "en_us" => ctrlProc.CtrlProcEnUs ?? ctrlProcId,
            _ => ctrlProcId
        };
    }

    /// <summary>
    /// 处理日期计算逻辑
    /// </summary>
    /// <param name="value">原始值</param>
    /// <param name="field">字段配置</param>
    /// <returns>计算后的值</returns>
    private static object ProcessDateCalculation(object? value, Model.MailCenter.TempField field)
    {
        // 只有当FileType为Datetime且Count字段不为空时才进行日期计算
        if (value == null ||
            !string.Equals(field.FileType, "Datetime", StringComparison.OrdinalIgnoreCase) ||
            string.IsNullOrEmpty(field.Count))
        {
            return value ?? string.Empty;
        }

        // 尝试将值转换为DateTime
        if (!TryParseDateTime(value, out var dateTime))
        {
            return value ?? string.Empty;
        }

        try
        {
            // 解析Count字段中的计算规则
            var calculatedDate = ParseAndCalculateDate(dateTime, field.Count);
            return calculatedDate;
        }
        catch
        {
            // 计算失败时返回原值
            return value ?? string.Empty;
        }
    }

    /// <summary>
    /// 尝试将值转换为DateTime
    /// </summary>
    /// <param name="value">要转换的值</param>
    /// <param name="dateTime">转换后的DateTime</param>
    /// <returns>是否转换成功</returns>
    private static bool TryParseDateTime(object value, out DateTime dateTime)
    {
        dateTime = default;

        if (value is DateTime dt)
        {
            dateTime = dt;
            return true;
        }

        if (value is string str && DateTime.TryParse(str, out dateTime))
        {
            return true;
        }

        return false;
    }

    /// <summary>
    /// 解析并计算日期
    /// </summary>
    /// <param name="baseDate">基础日期</param>
    /// <param name="countRule">计算规则，如[dm_1][ay_2]</param>
    /// <returns>计算后的日期</returns>
    private static DateTime ParseAndCalculateDate(DateTime baseDate, string countRule)
    {
        var result = baseDate;

        // 使用正则表达式匹配所有的计算规则 [操作类型单位_数值]
        var pattern = @"\[([da])([myd])_(\d+)\]";
        var matches = System.Text.RegularExpressions.Regex.Matches(countRule, pattern);

        foreach (System.Text.RegularExpressions.Match match in matches)
        {
            if (match.Groups.Count == 4)
            {
                var operation = match.Groups[1].Value; // d=减, a=加
                var unit = match.Groups[2].Value;      // m=月, y=年, d=日
                var amount = int.Parse(match.Groups[3].Value);

                // 根据操作类型确定是加还是减
                var actualAmount = operation == "d" ? -amount : amount;

                // 根据单位进行日期计算
                result = unit switch
                {
                    "y" => result.AddYears(actualAmount),   // 年
                    "m" => result.AddMonths(actualAmount),  // 月
                    "d" => result.AddDays(actualAmount),    // 日
                    _ => result // 未知单位，不进行计算
                };
            }
        }

        return result;
    }

    /// <summary>
    /// 格式化值
    /// </summary>
    /// <param name="value">要格式化的值</param>
    /// <param name="format">格式化字符串</param>
    /// <param name="key">字段键名，用于判断语言类型</param>
    /// <returns>格式化后的字符串</returns>
    private static string FormatValue(object? value, string? format, string key)
    {
        if (value == null) return string.Empty;

        if (string.IsNullOrEmpty(format))
        {
            return value.ToString() ?? string.Empty;
        }

        try
        {
            // 根据格式化字符串进行格式化
            if (value is DateTime dateTime)
            {
                var isEnglish = key.Contains("en")? "en-US" : "zh-CN";
                return dateTime.ToString(format, new CultureInfo(isEnglish));
            }
            else if (value is decimal || value is double || value is float)
            {
                return string.Format($"{{0:{format}}}", value);
            }
            else
            {
                return value.ToString() ?? string.Empty;
            }
        }
        catch
        {
            return value.ToString() ?? string.Empty;
        }
    }
}
