﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenter.Applications.Models.Receive;
using iPlatformExtension.MailCenter.Applications.Queries.Receive;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Receive
{
    /// <summary>
    /// 获取关联邮件列表
    /// </summary>
    internal sealed class GetRelateMailQueryHandler(
        IFreeSql<MailCenterFreeSql> freeSql,
        IUserInfoRepository userInfoRepository
    ) : IRequestHandler<GetRelateMailQuery, IEnumerable<GetRelateMailDto>>
    {
        public async Task<IEnumerable<GetRelateMailDto>> Handle(
            GetRelateMailQuery request,
            CancellationToken cancellationToken
        )
        {
            // 统一查询所有关联记录，同时连接收件和发件表来确定关联邮件的实际类型
            var list = await freeSql
                .Select<MailCorrelative, MailReceive, MailSend>()
                .WithLock()
                .LeftJoin((mc, mr, ms) =>
                    (mc.MailId == request.MailId ? mc.ObjId : mc.MailId) == mr.MailId)
                .LeftJoin((mc, mr, ms) =>
                    (mc.MailId == request.MailId ? mc.ObjId : mc.MailId) == ms.MailId)
                .Where((mc, mr, ms) => mc.MailId == request.MailId || mc.ObjId == request.MailId)
                .Where((mc, mr, ms) => mc.CorrelateType ==  SysEnum.CorrelateType.SendMail.ToString() ||  mc.CorrelateType ==  SysEnum.CorrelateType.ReceiveMail.ToString() )
                .OrderByDescending((mc, mr, ms) => mc.CreateTime)
                .Page(request.PageIndex!.Value, request.PageSize!.Value)
                .Count(out var totalCount)
                .ToListAsync((mc, mr, ms) => new GetRelateMailDto(
                    mc.Id,
                    mc.MailId == request.MailId ? mc.ObjId : mc.MailId,
                    mr.MailId != null ? "收件" : "发件",  // 根据实际数据确定类型
                    mc.CreateTime,
                    mr.MailId != null ? mr.MailNo : ms.MailNo,
                    mr.MailId != null ? mr.MailFrom : ms.MailFrom,
                    mr.MailId != null ? mr.MailSubject : ms.MailSubject,
                    mc.CreateBy,
                    mr.MailId != null ? mr.MailDate : ms.MailDate,
                    mr.MailId != null ? mr.Status : ms.Status,
                    mc.MailId == request.MailId ? mc.ObjId : mc.MailId
                ), cancellationToken);
            return new PageResult<GetRelateMailDto>()
            {
                Data = await list.ToAsyncEnumerable()
                    .SelectAwait(async receiveList =>
                    {
                        if (receiveList.RelateUserId is not null)
                        {
                            ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository =
                                userInfoRepository;
                            receiveList.RelateUser = new
                            {
                                CnName = (
                                    await userBaseInfoRepository.GetCacheValueAsync(
                                        receiveList.RelateUserId,
                                        cancellationToken: cancellationToken
                                    )
                                )?.CnName ?? "",
                                UserId = receiveList.RelateUserId,
                            };
                        }

                        return receiveList;
                    })
                    .ToListAsync(cancellationToken: cancellationToken),
                Page = request.PageIndex.Value,
                PageSize = request.PageSize.Value,
                Total = totalCount,
            };
        }
    }
}
