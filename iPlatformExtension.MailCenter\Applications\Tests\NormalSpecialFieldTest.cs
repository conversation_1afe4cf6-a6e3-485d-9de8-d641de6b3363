using iPlatformExtension.Model.MailCenter;

namespace iPlatformExtension.MailCenter.Applications.Tests;

/// <summary>
/// 普通特殊字段测试类
/// </summary>
public static class NormalSpecialFieldTest
{
    /// <summary>
    /// 测试当前日期特殊字段功能
    /// </summary>
    public static void TestDateTimeNowFields()
    {
        Console.WriteLine("=== 当前日期特殊字段测试开始 ===");

        // 测试用例1：datetime_now 默认格式
        TestCase1();

        // 测试用例2：datetime_now_en 英文格式
        TestCase2();

        // 测试用例3：datetime_now_cn 中文格式
        TestCase3();

        // 测试用例4：datetime_now_en 自定义格式
        TestCase4();

        // 测试用例5：datetime_now_cn 自定义格式
        TestCase5();

        Console.WriteLine("=== 当前日期特殊字段测试结束 ===");
    }

    private static void TestCase1()
    {
        Console.WriteLine("\n测试用例1：datetime_now 默认格式");
        
        var field = new TempField
        {
            Key = "datetime_now",
            FileType = "String",
            Format = "yyyy-MM-dd HH:mm:ss"
        };

        Console.WriteLine($"字段Key: {field.Key}");
        Console.WriteLine($"格式: {field.Format}");
        Console.WriteLine($"预期: 当前日期时间，格式为 yyyy-MM-dd HH:mm:ss");
        Console.WriteLine("测试结果: 需要通过实际运行验证");
    }

    private static void TestCase2()
    {
        Console.WriteLine("\n测试用例2：datetime_now_en 英文格式");
        
        var field = new TempField
        {
            Key = "datetime_now_en",
            FileType = "String",
            Format = null // 使用默认英文格式
        };

        Console.WriteLine($"字段Key: {field.Key}");
        Console.WriteLine($"格式: 默认英文格式");
        Console.WriteLine($"预期: 当前日期，格式为 January 1, 2024");
        Console.WriteLine("测试结果: 需要通过实际运行验证");
    }

    private static void TestCase3()
    {
        Console.WriteLine("\n测试用例3：datetime_now_cn 中文格式");
        
        var field = new TempField
        {
            Key = "datetime_now_cn",
            FileType = "String",
            Format = null // 使用默认中文格式
        };

        Console.WriteLine($"字段Key: {field.Key}");
        Console.WriteLine($"格式: 默认中文格式");
        Console.WriteLine($"预期: 当前日期，格式为 2024年1月1日");
        Console.WriteLine("测试结果: 需要通过实际运行验证");
    }

    private static void TestCase4()
    {
        Console.WriteLine("\n测试用例4：datetime_now_en 自定义格式");
        
        var field = new TempField
        {
            Key = "datetime_now_en",
            FileType = "String",
            Format = "dddd, MMMM dd, yyyy"
        };

        Console.WriteLine($"字段Key: {field.Key}");
        Console.WriteLine($"格式: {field.Format}");
        Console.WriteLine($"预期: 当前日期，格式为 Monday, January 01, 2024");
        Console.WriteLine("测试结果: 需要通过实际运行验证");
    }

    private static void TestCase5()
    {
        Console.WriteLine("\n测试用例5：datetime_now_cn 自定义格式");
        
        var field = new TempField
        {
            Key = "datetime_now_cn",
            FileType = "String",
            Format = "yyyy年MM月dd日 dddd"
        };

        Console.WriteLine($"字段Key: {field.Key}");
        Console.WriteLine($"格式: {field.Format}");
        Console.WriteLine($"预期: 当前日期，格式为 2024年01月01日 星期一");
        Console.WriteLine("测试结果: 需要通过实际运行验证");
    }
}
