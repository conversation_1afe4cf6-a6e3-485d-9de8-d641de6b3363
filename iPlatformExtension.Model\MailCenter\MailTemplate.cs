using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.MailCenter {

	[Table(Name = "mail_template", DisableSyncStructure = true)]
	public partial class MailTemplate {

		/// <summary>
		/// 模板主键ID
		/// </summary>
		[Column(Name = "template_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string TemplateId { get; set; }

		/// <summary>
		/// 申请类型
		/// </summary>
		[Column(Name = "apply_type_id")]
		public string ApplyTypeId { get; set; }

		/// <summary>
		/// 邮件模板内容
		/// </summary>
		[Column(Name = "body", StringLength = -2)]
		public string Body { get; set; }

		/// <summary>
		/// 案件流向
		/// </summary>
		[Column(Name = "case_direction", StringLength = 100)]
		public string CaseDirection { get; set; }

		/// <summary>
		/// 案件类型
		/// </summary>
		[Column(Name = "case_type", StringLength = 1500)]
		public string CaseType { get; set; }

		/// <summary>
		/// 国家
		/// </summary>
		[Column(Name = "country_id")]
		public string CountryId { get; set; }

		/// <summary>
		/// 创建用户
		/// </summary>
		[Column(Name = "create_by", StringLength = 50, IsNullable = false)]
		public string CreateBy { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[Column(Name = "create_time", DbType = "datetime")]
		public DateTime CreateTime { get; set; }

		/// <summary>
		/// 客户id
		/// </summary>
		[Column(Name = "customer_id", StringLength = 500)]
		public string CustomerId { get; set; }

		/// <summary>
		/// 是否有效
		/// </summary>
		[Column(Name = "is_enabled")]
		public bool? IsEnabled { get; set; }

		/// <summary>
		/// 是否进入PCT
		/// </summary>
		[Column(Name = "is_pct_enter")]
		public bool? IsPctEnter { get; set; }

		/// <summary>
		/// 是否相同客户
		/// </summary>
		[Column(Name = "is_same_customer")]
		public bool? IsSameCustomer { get; set; }

		/// <summary>
		/// 是否相同名称
		/// </summary>
		[Column(Name = "is_same_value")]
		public bool? IsSameValue { get; set; }

		/// <summary>
		/// 密送人字段，id;拼接
		/// </summary>
		[Column(Name = "mail_bcc_template", StringLength = 1500)]
		public string MailBccTemplate { get; set; }

		/// <summary>
		/// 抄送人字段，id;拼接
		/// </summary>
		[Column(Name = "mail_cc_template", StringLength = 1500)]
		public string MailCcTemplate { get; set; }

		/// <summary>
		/// 收件人字段，id;拼接
		/// </summary>
		[Column(Name = "mail_to_template", StringLength = 1500)]
		public string MailToTemplate { get; set; }

		/// <summary>
		/// 管理员用户，用;拼接
		/// </summary>
		[Column(Name = "manager_users")]
		public string ManagerUsers { get; set; }

		/// <summary>
		/// 模板名称
		/// </summary>
		[Column(Name = "name", StringLength = 50)]
		public string Name { get; set; }

		/// <summary>
		/// 限制名称id;拼接
		/// </summary>
		[Column(Name = "name_value", StringLength = 1500)]
		public string NameValue { get; set; }

		/// <summary>
		/// 脚本
		/// </summary>
		[Column(Name = "sql2", StringLength = -1)]
		public string Sql2 { get; set; }

		/// <summary>
		/// 模板编号
		/// </summary>
		[Column(Name = "template_no", StringLength = 50)]
		public string TemplateNo { get; set; }

		/// <summary>
		/// 模板类型(1普通模板,2单任务模板,3多任务模板,4单官文模板,5多官文模板)
		/// </summary>
		[Column(Name = "template_type", DbType = "int")]
		public int? TemplateType { get; set; }

		/// <summary>
		/// 邮件标题
		/// </summary>
		[Column(Name = "title", StringLength = -1)]
		public string Title { get; set; }

		/// <summary>
		/// 更新用户
		/// </summary>
		[Column(Name = "update_by", StringLength = 50)]
		public string UpdateBy { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[Column(Name = "update_time", DbType = "datetime")]
		public DateTime? UpdateTime { get; set; }

	}

}
