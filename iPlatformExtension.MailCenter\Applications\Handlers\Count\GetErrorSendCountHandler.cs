using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenter.Applications.Models.Count;
using iPlatformExtension.MailCenter.Applications.Queries.Count;
using iPlatformExtension.MailCenter.HostedService;
using iPlatformExtension.Model.MailCenter;
using MediatR;
using Microsoft.Extensions.Logging;
using static iPlatformExtension.Model.Enum.SysEnum;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Count
{
    public class GetErrorSendCountHandler(
         ILogger<InitMailCountService> logger,
        IFreeSql<MailCenterFreeSql> freeSql,
        IRedisCache<RedisCacheOptionsBase> redisCache,
        IFreeSql<PlatformFreeSql> platformFreeSql
        ) : IRequestHandler<GetErrorSendCountQuery, Dictionary<string, CacheValueModel>>
    {
        public async Task<Dictionary<string, CacheValueModel>> Handle(GetErrorSendCountQuery request, CancellationToken cancellationToken)
        {
            try
            {
                var defaultDict = request.defaultDict;
                var cacheTime = request._cacheTime ?? TimeSpan.FromMinutes(5);
                logger.LogInformation("开始统计发送失败邮件数量");
                var count = await freeSql
                    .Select<MailSend, MailSendFlow>()
                    .LeftJoin(o => o.t1.MailId == o.t2.MailId)
                    .Where(o =>
                        o.t1.Status == SendStatusType.Error.GetHashCode()
                    )
                    .WithLock()
                    .GroupBy(o => o.t2.UndertakeUserId)
                    .ToDictionaryAsync(
                        it => new CacheValueModel { Count = it.Count(), CreateTime = DateTime.Now },
                        cancellationToken
                    );

                // 将查询结果合并到默认字典中
                foreach (var item in count)
                {
                    defaultDict[item.Key] = item.Value;
                }

                await redisCache.RemoveCacheKeyAsync("ErrorSendCount", cancellationToken);
                await redisCache.SetCacheValuesAsync(
                    "ErrorSendCount",
                    defaultDict,
                    cacheTime,
                    cancellationToken: cancellationToken
                );

                logger.LogInformation("发送失败邮件数量统计完成，共{UserCount}个", count.Count);
                return defaultDict;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "发送失败邮件数量统计统异常");
                return request.defaultDict;
            }
        }
    }
} 