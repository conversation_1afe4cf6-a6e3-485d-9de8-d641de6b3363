﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.MailCenter.Applications.Models.Count;
using iPlatformExtension.MailCenter.Applications.Queries.Count;
using iPlatformExtension.MailCenter.Applications.Queries.SysConfig;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using MediatR;
using MongoDB.Bson;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Count;

internal class RedisCacheHandle(IRedisCache<RedisCacheOptionsBase> redisCache, IMediator mediator, IFreeSql freeSql)
{
    private readonly TimeSpan _cacheTime = TimeSpan.FromMinutes(15);

    private async Task<Dictionary<string, CacheValueModel>> GetAllotCount(
        CancellationToken cancellationToken
    )
    {
        var count = await freeSql
            .Select<MailReceive>()
            .Where(it => it.Status == 2)
            .WithLock()
            .GroupBy(it => it.HostId)
            .ToDictionaryAsync(
                it => new CacheValueModel { Count = it.Count(), CreateTime = DateTime.Now },
                cancellationToken
            );
        await redisCache.RemoveCacheKeyAsync("Allot", cancellationToken);
        await redisCache.SetCacheValueAsync(
            "Allot",
            count,
            _cacheTime,
            cancellationToken: cancellationToken
        );
        return count;
    }

    private async Task<Dictionary<string, CacheValueModel>> GetHandleCount(
        CancellationToken cancellationToken
    )
    {
        // 获取审核用户和对应的数量统计
        var count = await freeSql
            .Select<MailReceive, FlowRecord>()
            .InnerJoin(it =>
                it.t1.MailId == it.t2.MailId
                && it.t2.IsCurrent == SysEnum.Status.Enable.GetHashCode()
            )
            .Where(it => it.t2 != null)
            .WithLock()
            .GroupBy(it => it.t2.AuditUser)
            .ToDictionaryAsync(
                it => new CacheValueModel { Count = it.Count(), CreateTime = DateTime.Now },
                cancellationToken
            );
        await redisCache.RemoveCacheKeyAsync("Handle", cancellationToken);
        await redisCache.SetCacheValuesAsync(
            "Handle",
            count,
            _cacheTime,
            cancellationToken: cancellationToken
        );
        return count;
    }

    private async Task<Dictionary<string, CacheValueModel>> GetReaderListCount(
        CancellationToken cancellationToken
    )
    {
        var count = await freeSql
            .Select<MailReaderList, MailReceive>()
            .LeftJoin(o => o.t1.MailId == o.t2.MailId)
            .Where(o =>
                o.t1.Status == (int)ReaderStatusEnum.ToRead
                && o.t2.Status >= SysEnum.ReceiveFileType.Handle.GetHashCode()
            )
            .WithLock()
            .GroupBy(o => o.t1.UserId)
            .ToDictionaryAsync(
                it => new CacheValueModel { Count = it.Count(), CreateTime = DateTime.Now },
                cancellationToken
            );
        await redisCache.RemoveCacheKeyAsync("ReaderList", cancellationToken);
        await redisCache.SetCacheValuesAsync(
            "ReaderList",
            count,
            _cacheTime,
            cancellationToken: cancellationToken
        );
        return count;
    }

    public async Task<IEnumerable<MailDto>> GetCount(
        string userId,
        CancellationToken cancellationToken
    )
    {
        if (string.IsNullOrEmpty(userId))
        {
            return Array.Empty<MailDto>();
        }

        var hostIds = await mediator.Send(
            new GetUserAccessQuery(userId, "sorter"),
            cancellationToken
        );

        var handleCache = await redisCache.GetCacheValueAsync<string, CacheValueModel>(
            "Handle",
            userId,
            cancellationToken
        );

        var readCache = await redisCache.GetCacheValueAsync<string, CacheValueModel>(
            "ReaderList",
            userId,
            cancellationToken
        );

        var allotCache = (
            await redisCache.GetCacheKeyValuesAsync<string, CacheValueModel>(
                "Allot",
                cancellationToken
            )
        );

            var auditMailCache = (
            await redisCache.GetCacheKeyValuesAsync<string, CacheValueModel>(
            "AuditSendMailCount",
            cancellationToken
            )
            );

            var errorSendCache = (
            await redisCache.GetCacheKeyValuesAsync<string, CacheValueModel>(
            "ErrorSendCount",
            cancellationToken
            )
            );

            var scheduledSendCache = (
            await redisCache.GetCacheKeyValuesAsync<string, CacheValueModel>(
            "ScheduledSendCount",
            cancellationToken
            )
            );
            var sendReaderListCache = (
            await redisCache.GetCacheKeyValuesAsync<string, CacheValueModel>(
            "SendReaderList",
            cancellationToken
            )
            );

        return new List<MailDto>()
        {
            new("Handle", handleCache?.Count ?? 0),
            new(
                "Allot",
                allotCache
                    .Where(it => hostIds.HostId.Contains(it.Key))
                    .Select(it => it.Value.Count)
                    .Sum()
            ),
            new("ReaderList", readCache?.Count ?? 0),
            new("ScheduledSendCount", scheduledSendCache?.Count ?? 0),
            new("ErrorSendCount", errorSendCache?.Count ?? 0),
            new("AuditMailCount", auditMailCache?.Count ?? 0),
            new("SendReaderList", sendReaderListCache?.Count ?? 0),
        };
    }

    protected async Task UpdateHandleCache(string key,
        string auditUser,
        int countChange,
        DateTime tsDateTime,
        CancellationToken cancellationToken
    )
    {

        var handleCache = await redisCache.GetCacheValueAsync<string, CacheValueModel>(
            key,
            auditUser,
            cancellationToken
        );

        if (handleCache is not null && tsDateTime >= handleCache.CreateTime)
        {
            handleCache.Count += countChange;
            handleCache.CreateTime = tsDateTime;
            await redisCache.SetCacheValueAsync(
                key,
                auditUser,
                handleCache,
                true,
                cancellationToken: cancellationToken
            );
        }
    }
}