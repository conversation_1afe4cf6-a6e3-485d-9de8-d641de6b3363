@MailCenter_HostAddress = http://localhost:8081
@TrademarkOperationCenter_HostAddress = http://localhost:5098

@Authorization = bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTMzNTE4NzksIm5hbWUiOm51bGwsImlzcyI6Imlwci5hY2lwbGF3LmNvbSIsInN1YiI6ImVhOTM3ZGE4LTg1ZmUtNDk4ZC05ODQzLTE3ZTdkNjE2YzY5MiIsImF1ZCI6InBhdGFzLmFjaXBsYXcuY29tIn0.9C7JlkFIfU7qmPCRQGdWyA5hHUTDItlVTsUTPl7xWB4

GET {{MailCenter_HostAddress}}/Platform/GetProcList?ProcId=0eb10ac0-cec6-4131-80ee-6974d31cb61c
Accept: application/json
###
GET {{MailCenter_HostAddress}}/Platform/PlatformRelateCustomer?CustomerId=6bdcbdbd-4e9a-4347-a38b-112d8166396f&PageSize=999&PageIndex=1
Accept: application/json
###
GET {{MailCenter_HostAddress}}/Receive/GetRelateCustomer?MailId=6bdcbdbd-4e9a-4347-a38b-112d8166396f&PageSize=999&PageIndex=1
Accept: application/json
###
GET {{MailCenter_HostAddress}}/Receive/GetReceiveDetail?MailId=ed098dc5-b81a-4891-9f41-364ddc2b40e6
Accept: application/json
###
GET {{MailCenter_HostAddress}}/Receive/GetReceiveList?PageIndex=1&PageSize=10&MailSubject=AP25101227SU
Accept: application/json
Authorization:bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NDQwMDgwNTUsIm5hbWUiOm51bGwsImlzcyI6Imlwci5hY2lwbGF3LmNvbSIsInN1YiI6IjRkYWUwMjk3LTllNDEtNDFiZC1hMGJlLWViOTZjOGZiYzU1YiIsImF1ZCI6InBhdGFzLmFjaXBsYXcuY29tIn0.3aYB5FBAJSlvIZVQTWlbzNe1Llqd7IUGtqvettJIhRo

###
GET {{MailCenter_HostAddress}}/Send/GetRelatedTaskFiles?MailId=5ab5b9ac-f25e-433b-aa15-666da3f70bbd&FileSource=1
Accept: application/json
Authorization:bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NDQwMDgwNTUsIm5hbWUiOm51bGwsImlzcyI6Imlwci5hY2lwbGF3LmNvbSIsInN1YiI6IjRkYWUwMjk3LTllNDEtNDFiZC1hMGJlLWViOTZjOGZiYzU1YiIsImF1ZCI6InBhdGFzLmFjaXBsYXcuY29tIn0.3aYB5FBAJSlvIZVQTWlbzNe1Llqd7IUGtqvettJIhRo

###
GET {{MailCenter_HostAddress}}/Platform/GetRelateCaseList?CaseId=630dc2ec-44b1-49ca-8498-20cd2ddb90a1
Accept: application/json
Authorization:bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NDQwMDgwNTUsIm5hbWUiOm51bGwsImlzcyI6Imlwci5hY2lwbGF3LmNvbSIsInN1YiI6IjRkYWUwMjk3LTllNDEtNDFiZC1hMGJlLWViOTZjOGZiYzU1YiIsImF1ZCI6InBhdGFzLmFjaXBsYXcuY29tIn0.3aYB5FBAJSlvIZVQTWlbzNe1Llqd7IUGtqvettJIhRo


###
POST {{MailCenter_HostAddress}}/Send/GetMailList
Content-Type: application/json
Authorization: {{Authorization}}

{"mailFrom":"<EMAIL>","pageIndex":1,"pageSize":10}

###
POST {{MailCenter_HostAddress}}/Send/WriteMail
Content-Type: application/json
Authorization: {{Authorization}}

{"mailId":"e594fd6b-a060-4e30-83cd-40a7ffc4d712","mailFrom":[{"mailAddress":"<EMAIL>","displayName":"陈碧燕"}],"mailTo":[{"mailAddress":"<EMAIL>","displayName":""}],"mailCc":[],"mailBcc":[],"sendTime":null,"attachments":[{"attachmentId":"91f13615-77a1-4cc1-8d77-9784c715f379","attachmentType":2},{"attachmentId":"9db3c8c0-c454-4c75-8802-c14a592ff2aa","attachmentType":2},{"attachmentId":"631902b2-6730-46a0-bdf6-548e13097915","attachmentType":2}],"mailRelayBody":"","isImportant":false,"isRead":false,"isRequiredProcessTime":false,"hostId":"ae7dc38f-aaf4-4748-ba59-0dfb72e4119b","mailEmlUrl":null,"regionMailId":null,"mailSubject":"上传","signatureBody":"<p>--</p>","mailHtmlBody":"<p>aabbccdd</p>","files":[{"fileName":"320x480-new.png","fileNo":"a00230975463","fileSource":2,"attachmentType":2}]}

###
GET {{MailCenter_HostAddress}}/Receive/GetRelateMail?MailId=392f413f-db80-4498-b5e8-b538ac357abb&PageIndex=1&PageSize=10
Content-Type: application/json
Authorization: {{Authorization}}

###
GET {{MailCenter_HostAddress}}/Template/GetMailTemplateList?Search=&NoticesIds=6FBA7E97-AB90-48B5-A503-40AA64BC22C4&PageSize=10&PageIndex=1
Content-Type: application/json
Authorization: {{Authorization}}

###
GET {{MailCenter_HostAddress}}/Process/RecentlySendProcessed?pageIndex=1&pageSize=10&Type=0&Search=
Content-Type: application/json
Authorization: {{Authorization}}


###
GET {{MailCenter_HostAddress}}/Process/RecentlySendProcessed?pageIndex=1&pageSize=10&Type=0&Search=
Content-Type: application/json
Authorization: {{Authorization}}


###
POST {{MailCenter_HostAddress}}/Platform/GetProcList
Content-Type: application/json

{
  "procId": "0eb10ac0-cec6-4131-80ee-6974d31cb61c"
}


###
POST {{MailCenter_HostAddress}}/Template/PreviewTemplate
Content-Type: application/json
Authorization: {{Authorization}}

{"templateId":"74b8ee24-334e-4408-a162-765727fc768a","hostId":"20ac9fe7-4bf2-49e9-b29d-6e62da1b0d44","signatureBody":"<p>2025年7月24日09:36:52</p>","isImportant":false,"isRead":false,"isRequiredProcessTime":false,"sendTime":"","procIds":["b0393ef4-9d1c-4390-8408-8b25bf6c3089","bfa0ef55-2fdb-43c8-9f9a-4801b7077866"]}

###
POST {{MailCenter_HostAddress}}/Signature/SaveSignature
Content-Type: application/json

{"name":"","content":"","id":"6be76805-596c-438f-8b3b-439592ac3209"}

###
POST {{MailCenter_HostAddress}}/Receive/SetRelate
Content-Type: application/json

{"mailId":"6bdcbdbd-4e9a-4347-a38b-112d8166396f","correlateType":"Customer","relateIdList":["bca428ce-cb0a-48ab-8012-f4299b9d7534"]}

###
POST {{MailCenter_HostAddress}}/AnalysisRule/AnalysisRule
Content-Type: application/json

{"mailId":["522bf045-027d-492d-b82e-2be0716f8b33","4195a5b4-2b64-41cd-9f48-3abef2a4b7d6"]}
###
POST {{MailCenter_HostAddress}}/Receive/SetSendName
Content-Type: application/json

{"mailId":"d9a5da56-a34b-40c8-9ff4-4fef935fc430","SendName":"这是邮件名称"}

###
GET {{MailCenter_HostAddress}}/Count
Accept: application/json
Authorization:bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NDM2NDUyODYsIm5hbWUiOm51bGwsImlzcyI6Imlwci5hY2lwbGF3LmNvbSIsInN1YiI6ImMxNTk3YzUxLTc4MWItNDY4Mi04MWM3LWUwMWRjMjM0MzFjZSIsImF1ZCI6InBhdGFzLmFjaXBsYXcuY29tIn0.mJPGiW7avRF9scj42DlXK1UQryvO0DWkq7a0CqQQ4gE

###
GET {{MailCenter_HostAddress}}/Receive/GetActionUserList?MailId=abed023c-9551-48a5-90a3-7d7d498b74af&Action=Submit&Flow=Send
Accept: application/json
Authorization:bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NDM2NDUyODYsIm5hbWUiOm51bGwsImlzcyI6Imlwci5hY2lwbGF3LmNvbSIsInN1YiI6ImMxNTk3YzUxLTc4MWItNDY4Mi04MWM3LWUwMWRjMjM0MzFjZSIsImF1ZCI6InBhdGFzLmFjaXBsYXcuY29tIn0.mJPGiW7avRF9scj42DlXK1UQryvO0DWkq7a0CqQQ4gE


###
GET  {{MailCenter_HostAddress}}/Customer/GetCustomerList?page=1&pageSize=20&customerIds=bca428ce-cb0a-48ab-8012-f4299b9d7534&isEnabled=true&contactName=&contactType=C8D462E6-CECE-4531-9046-B3D9B59C2023
Content-Type: application/json

###

###
GET  {{MailCenter_HostAddress}}/Customer/GetCustomerList?pageIndex=2&pageSize=20&customerIds=bca428ce-cb0a-48ab-8012-f4299b9d7534&isEnabled=true&contactName=
Content-Type: application/json

###
###
PUT  {{TrademarkOperationCenter_HostAddress}}/Count
Accept: application/json
Authorization:bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NDM2NDUyODYsIm5hbWUiOm51bGwsImlzcyI6Imlwci5hY2lwbGF3LmNvbSIsInN1YiI6ImMxNTk3YzUxLTc4MWItNDY4Mi04MWM3LWUwMWRjMjM0MzFjZSIsImF1ZCI6InBhdGFzLmFjaXBsYXcuY29tIn0.mJPGiW7avRF9scj42DlXK1UQryvO0DWkq7a0CqQQ4gE

{"teamId":"228929697877735","teamName":"OPPO团队","isExclusive":true,"isEffect":true,"teamDescription":"op","AuthorizeUser":"5da0647e-5286-4779-9198-a16cc06cc1b8;bd4c7f7d-6d4b-4035-9c0d-e925c6fc9045","createTime":"2024-04-03 15:34:57","seq":4,"customerId":["6B3A0677-789C-4F21-9994-591549D4E843"],"members":[{"teamMemberId":"228929697896152","userId":"48577a67-b9af-4308-b747-968ba3afe385","userName":"吴春晓","roleId":"03804687-683a-43d8-ad23-c652fc8ed6ca","roleName":"团队负责人","deptName":"国内商标交付二组"},{"teamMemberId":"229989531012256","userId":"ff9cf8db-96f2-4f70-9764-ad6bae7a3cea","userName":"张丽莹","roleId":"b1c67ef7-9170-49fa-93ab-9215e5d8c5e9","roleName":"导师","deptName":"国内商标交付二组"},{"teamMemberId":"229989531067202","userId":"f6f4e679-6a35-43d5-9ae3-49aff2f6901f","userName":"王福英","roleId":"b12b0dcb-e656-4345-93ff-fca7f75204fa","roleName":"代理人","deptName":"国内商标交付二组"},{"teamMemberId":"229989531075045","userId":"e8cf5d42-6c1f-4555-abb7-acad09626efc","userName":"王斌","roleId":"b12b0dcb-e656-4345-93ff-fca7f75204fa","roleName":"代理人","deptName":"国内商标交付二组"},{"teamMemberId":"244399686984109","userId":"bc70f722-ad61-4f2f-b7f7-e46699e7079e","userName":"陈睿祺","roleId":"b12b0dcb-e656-4345-93ff-fca7f75204fa","roleName":"代理人","deptName":"国内商标交付二组"}]}
Authorization:bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NDQ0MjI3MzgsIm5hbWUiOm51bGwsImlzcyI6Imlwci5hY2lwbGF3LmNvbSIsInN1YiI6ImMxNTk3YzUxLTc4MWItNDY4Mi04MWM3LWUwMWRjMjM0MzFjZSIsImF1ZCI6InBhdGFzLmFjaXBsYXcuY29tIn0.gOkmsSctBhj2q72PvVqW6S_yctcgapNZrABSWLe-ZyI

###
# 修改附件名称接口测试
POST {{MailCenter_HostAddress}}/Send/UpdateAttachmentName
Content-Type: application/json
Authorization: {{Authorization}}

{
  "attachmentId": "your-attachment-id-here",
  "realName": "新的文件名.pdf"
}

###


###
POST {{MailCenter_HostAddress}}/Template/PreviewTemplate
Content-Type: application/json
Authorization: {{Authorization}}

{"templateId":"b6ead5f7-a5ed-42d4-9045-573c6b69cf66","hostId":"20ac9fe7-4bf2-49e9-b29d-6e62da1b0d44","signatureBody":"<p>1</p>","isImportant":true,"isRead":false,"isRequiredProcessTime":false,"sendTime":"","procIds":["b0393ef4-9d1c-4390-8408-8b25bf6c3089","bfa0ef55-2fdb-43c8-9f9a-4801b7077866"]}