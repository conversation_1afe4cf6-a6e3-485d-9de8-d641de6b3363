using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Models.Template;
using iPlatformExtension.MailCenter.Applications.Queries.Template;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Constants;

using iPlatformExtension.Model.Dto;
using iPlatformExtension.Repository.Interface;
using MediatR;
using static iPlatformExtension.Model.Enum.SysEnum;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Template
{
    /// <summary>
    /// 获取邮件模板列表查询处理程序
    /// </summary>
    internal sealed class GetMailTemplateListQueryHandler(
        IMailTemplateRepository mailTemplateRepository,
        IUserInfoRepository userInfoRepository,
        IFreeSql<PlatformFreeSql> freeSql,
        IHttpContextAccessor httpContextAccessor
    ) : IRequestHandler<GetMailTemplateListQuery, PageResult<GetMailTemplateListDto>>
    {
        public async Task<PageResult<GetMailTemplateListDto>> Handle(GetMailTemplateListQuery request, CancellationToken cancellationToken)
        {
            // 获取当前用户ID
            var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
            if (string.IsNullOrWhiteSpace(userId)) throw new ApplicationException("用户未登录");
            //获取用户角色
            var mailSuperManager = await freeSql.Select<SysUserRole, SysRoleInfo>()
            .LeftJoin(it => it.t1.RoleId == it.t2.RoleId)
            .Where(it => it.t1.UserId == userId && it.t2.RoleCode == "MailSuperManager")
            .ToListAsync(cancellationToken);

            var query = mailTemplateRepository
                .WhereIf(request.TemplateType != null && request.TemplateType.Count > 0, t => request.TemplateType.Contains(t.TemplateType.Value))
                .WhereIf(!string.IsNullOrWhiteSpace(request.Search), t =>
                    t.Name.Contains(request.Search!) ||
                    t.TemplateNo.Contains(request.Search!))
                .WhereIf(!mailSuperManager.Any() &&
                (request.NoticesIds == null || request.NoticesIds.Count == 0) &&
                 (request.ProcIds == null || request.ProcIds.Count == 0),
                it => it.ManagerUsers.Contains(userId))
                .WhereIf(request.IsEnabled.HasValue, t => t.IsEnabled == request.IsEnabled);

            // 收集所有需要处理的ProcId和NoticeId相关数据
            var allProcIds = new List<string>();
            var noticeFileDescIds = new List<string>();

            // 添加直接传入的任务ID
            if (request.ProcIds?.Count > 0)
            {
                allProcIds.AddRange(request.ProcIds);
            }

            if (request.NoticesIds?.Count > 0)
            {
                // 根据案件类型选择不同的官文查询逻辑
                if (request.CaseTypeId == CaseType.Trade) // 商标案件
                {
                    // 商标官文查询：直接从tm_case_notice_content获取ProcId和FileDescId
                    var trademarkData = await freeSql.Select<TmCaseNoticeContent>()
                        .WithLock()
                        .Where(x => request.NoticesIds.Contains(x.FileId))
                        .ToListAsync(x => new { x.ProcId, x.FileDescId }, cancellationToken);

                    // 提取ProcId
                    var procIds = trademarkData
                        .Where(x => !string.IsNullOrEmpty(x.ProcId))
                        .Select(x => x.ProcId!)
                        .Distinct();
                    allProcIds.AddRange(procIds);

                    // 提取FileDescId
                    var fileDescIds = trademarkData
                        .Where(x => !string.IsNullOrEmpty(x.FileDescId))
                        .Select(x => x.FileDescId!)
                        .Distinct();
                    noticeFileDescIds.AddRange(fileDescIds);
                }
                else // 专利等其他案件类型
                {
                    // 专利官文查询：从case_notice_office获取ProcId，通过FileCode查询FileDescId
                    var noticeData = await freeSql.Select<CaseNoticeOffice>()
                        .WithLock()
                        .Where(notice => request.NoticesIds.Contains(notice.NoticeId))
                        .ToListAsync(notice => new { notice.ProcId, notice.FileCode }, cancellationToken);

                    if (noticeData.Count > 0)
                    {
                        // 提取ProcId
                        var procIds = noticeData
                            .Where(n => !string.IsNullOrEmpty(n.ProcId))
                            .Select(n => n.ProcId!)
                            .Distinct();
                        allProcIds.AddRange(procIds);

                        // 通过FileCode查询FileDescId
                        var fileCodes = noticeData
                            .Where(n => !string.IsNullOrEmpty(n.FileCode))
                            .Select(n => n.FileCode!)
                            .Distinct()
                            .ToList();

                        if (fileCodes.Count > 0)
                        {
                            var fileDescIds = await freeSql.Select<BasFileDesc>()
                                .WithLock()
                                .Where(fd => fd.TextCode != null && fileCodes.Contains(fd.TextCode))
                                .ToListAsync(fd => fd.FileDescId, cancellationToken);

                            noticeFileDescIds.AddRange(fileDescIds.Where(fid => !string.IsNullOrEmpty(fid)));
                        }
                    }
                }
            }

            // 去重处理
            allProcIds = allProcIds.Distinct().ToList();

            // 当ProcIds与NoticesIds都为空时，不需要进行过滤，直接返回基本查询结果
            if ((request.ProcIds?.Count > 0) || (request.NoticesIds?.Count > 0))
            {
                // 定义默认值，用于处理没有任务数据的情况
                var caseTypeIds = new List<string>();
                var applyTypeIds = new List<string>();
                var caseDirections = new List<string>();
                var countryIds = new List<string>();
                var customerIds = new List<string>();
                var pctEnterValues = new List<bool> { false };
                var ctrlProcIds = new List<string>();
                var isSameCustomer = true; // 默认为相同客户
                var isSameNameValue = true; // 默认为相同NameValue

                // 只有在有任务ID时才查询任务数据
                if (allProcIds.Count > 0)
                {
                    // 使用联表查询一次性获取所有需要的数据，减少数据库查询次数
                    var caseData = await freeSql.Select<CaseProcInfo>().WithLock()
                        .LeftJoin<CaseInfo>((proc, caseInfo) => proc.CaseId == caseInfo.Id)
                        .Where(proc => allProcIds.Contains(proc.ProcId))
                        .ToListAsync(cancellationToken);

                    if (caseData.Count > 0)
                    {
                        // 提取所有可能的值用于模板匹配
                        caseTypeIds = caseData.Select(proc => proc.CaseInfo?.CaseTypeId).Where(ct => !string.IsNullOrEmpty(ct)).Distinct().ToList()!;
                        applyTypeIds = caseData.Select(proc => proc.CaseInfo?.ApplyTypeId).Where(at => !string.IsNullOrEmpty(at)).Distinct().ToList()!;
                        caseDirections = caseData.Select(proc => proc.CaseInfo?.CaseDirection).Where(cd => !string.IsNullOrEmpty(cd)).Distinct().ToList()!;
                        countryIds = caseData.Select(proc => proc.CaseInfo?.CountryId).Where(c => !string.IsNullOrEmpty(c)).Distinct().ToList()!;
                        customerIds = caseData.Select(proc => proc.CaseInfo?.CustomerId).Where(c => !string.IsNullOrEmpty(c)).Distinct().ToList()!;
                        pctEnterValues = caseData.Select(proc => proc.CaseInfo?.PctEnter ?? false).Distinct().ToList();
                        ctrlProcIds = caseData.Select(proc => proc.CtrlProcId).Where(cp => !string.IsNullOrEmpty(cp)).Distinct().ToList();

                        // 判断是否相同客户和相同NameValue
                        isSameCustomer = customerIds.Count <= 1; // 客户ID唯一或为空表示相同客户
                        isSameNameValue = request.NoticesIds?.Count > 0 ? noticeFileDescIds.Distinct().Count() <= 1 : ctrlProcIds.Count <= 1; // NameValue唯一表示相同NameValue
                    }
                }

                // 只查询需要过滤的字段，减少数据传输量和内存占用
                var templateFilterData = await query
                        .ToListAsync(t => new
                        {
                            t.TemplateId,
                            t.CaseType,
                            t.ApplyTypeId,
                            t.CaseDirection,
                            t.CountryId,
                            t.CustomerId,
                            t.TemplateType,
                            t.IsPctEnter,
                            t.NameValue,
                            t.IsSameCustomer,
                            t.IsSameValue
                        }, cancellationToken);

                // 在内存中过滤模板（处理分号分隔的字段）
                var filteredTemplateIds = templateFilterData.Where(template =>
                {
                    // 案件类型匹配：模板的CaseType为空或其分号分隔的值包含所有任务的案件类型ID
                    var caseTypeMatches = string.IsNullOrEmpty(template.CaseType) ||
                                         (caseTypeIds.Count > 0 &&
                                          caseTypeIds.All(ct => !string.IsNullOrEmpty(ct) &&
                                              template.CaseType.Split(';', StringSplitOptions.RemoveEmptyEntries).Contains(ct)));

                    // 申请类型匹配：模板的ApplyTypeId为空或其分号分隔的值包含所有任务的申请类型
                    var applyTypeMatches = string.IsNullOrEmpty(template.ApplyTypeId) ||
                                          (applyTypeIds.Count > 0 &&
                                           applyTypeIds.All(at => !string.IsNullOrEmpty(at) &&
                                               template.ApplyTypeId.Split(';', StringSplitOptions.RemoveEmptyEntries).Contains(at)));

                    // 案件流向匹配：模板的CaseDirection为空或其分号分隔的值包含所有任务的案件流向
                    var caseDirectionMatches = string.IsNullOrEmpty(template.CaseDirection) ||
                                              (caseDirections.Count > 0 &&
                                               caseDirections.All(cd => !string.IsNullOrEmpty(cd) &&
                                                   template.CaseDirection.Split(';', StringSplitOptions.RemoveEmptyEntries).Contains(cd)));

                    // 国家匹配：模板的CountryId为空或其分号分隔的值包含所有任务的国家
                    var countryMatches = string.IsNullOrEmpty(template.CountryId) ||
                                        (countryIds.Count > 0 &&
                                         countryIds.All(c => !string.IsNullOrEmpty(c) &&
                                             template.CountryId.Split(';', StringSplitOptions.RemoveEmptyEntries).Contains(c)));

                    // 客户ID匹配：模板的CustomerId为空或其分号分隔的值包含所有任务的客户ID
                    var customerMatches = string.IsNullOrEmpty(template.CustomerId) ||
                                         (customerIds.Count > 0 &&
                                          customerIds.All(c => !string.IsNullOrEmpty(c) &&
                                              template.CustomerId.Split(';', StringSplitOptions.RemoveEmptyEntries).Contains(c)));

                    // IsPctEnter匹配：模板的IsPctEnter为空或需要同时满足所有案件的PctEnter状态
                    var pctEnterMatches = !template.IsPctEnter.HasValue ||
                                         (pctEnterValues.Count > 0 &&
                                          pctEnterValues.All(pct => template.IsPctEnter.Value == pct));

                    // NameValue匹配：根据NoticesId和ProcId情况进行区分判断
                    var nameValueMatches = string.IsNullOrEmpty(template.NameValue) ||
                                          (request.NoticesIds != null && request.NoticesIds.Count > 0 &&
                                           noticeFileDescIds.Count > 0 &&
                                           noticeFileDescIds.All(fid => !string.IsNullOrEmpty(fid) &&
                                               template.NameValue.Split(';', StringSplitOptions.RemoveEmptyEntries).Contains(fid))) ||
                                          (request.ProcIds != null && request.ProcIds.Count > 0 &&
                                           ctrlProcIds.Count > 0 &&
                                           ctrlProcIds.All(cp => !string.IsNullOrEmpty(cp) &&
                                               template.NameValue.Split(';', StringSplitOptions.RemoveEmptyEntries).Contains(cp)));

                    // IsSameCustomer匹配：模板配置为空或为否时不限制，配置为是时需要限制相同客户
                    var sameCustomerMatches = !template.IsSameCustomer.HasValue ||
                                             !template.IsSameCustomer.Value ||
                                             (template.IsSameCustomer.Value && isSameCustomer);

                    // IsSameValue匹配：模板配置为空或为否时不限制，配置为是时需要限制相同NameValue
                    var sameValueMatches = !template.IsSameValue.HasValue ||
                                          !template.IsSameValue.Value ||
                                          (template.IsSameValue.Value && isSameNameValue);

                    // 只有当ProcIds或NoticesIds不为空时才进行模板类型过滤
                    var templateTypeMatches = (request.NoticesIds != null && request.NoticesIds.Count > 0 &&
                                              (template.TemplateType == MailTemplateType.SingleOfficial.GetHashCode() ||
                                               template.TemplateType == MailTemplateType.MultiOfficial.GetHashCode())) ||
                                             (request.ProcIds != null && request.ProcIds.Count > 0 &&
                                              (template.TemplateType == MailTemplateType.SingleTask.GetHashCode() ||
                                               template.TemplateType == MailTemplateType.MultiTask.GetHashCode()));

                    // 返回所有条件的组合结果
                    return caseTypeMatches && applyTypeMatches && caseDirectionMatches &&
                           countryMatches && customerMatches && pctEnterMatches &&
                           nameValueMatches && sameCustomerMatches && sameValueMatches &&
                           templateTypeMatches;
                })
                .Select(t => t.TemplateId)
                .ToList();

                // 将过滤后的结果转换为查询结果
                query = query.Where(t => filteredTemplateIds.Contains(t.TemplateId));
            }

            var totalCount = await query.CountAsync(cancellationToken);

            var result = await query
                .OrderByDescending(t => t.CreateTime)
                .Page(request.PageIndex, request.PageSize)
                .ToListAsync(t => new GetMailTemplateListDto(
                    t.TemplateId,
                    t.TemplateNo,
                    t.Name,
                    t.Title,
                    t.CreateTime,
                    t.CreateBy,
                    t.UpdateTime,
                    t.UpdateBy,
                    t.IsEnabled,
                    t.TemplateType,
                    string.Empty, // TemplateTypeName 将在后续处理中填充
                    t.ManagerUsers ?? string.Empty,
                    null // ManagerUserList 将在后续处理中填充
                ), cancellationToken);

            // 处理模板类型名称、管理员用户信息、创建人和更新人信息
            var lastResult = await result.ToAsyncEnumerable()
                .SelectAwait(async it =>
                {
                    ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository = userInfoRepository;

                    // 设置模板类型名称
                    it = it with
                    {
                        TemplateTypeName = it.TemplateType.HasValue ?
                            ((MailTemplateType)it.TemplateType.Value).GetDescription() :
                            string.Empty
                    };

                    // 处理创建人信息
                    if (!string.IsNullOrEmpty(it.CreateBy))
                    {
                        var createUserInfo = await userBaseInfoRepository.GetCacheValueAsync(it.CreateBy);
                        it.CreateUser = new
                        {
                            UserId = it.CreateBy,
                            CnName = createUserInfo?.CnName ?? ""
                        };
                    }

                    // 处理更新人信息
                    if (!string.IsNullOrEmpty(it.UpdateBy))
                    {
                        var updateUserInfo = await userBaseInfoRepository.GetCacheValueAsync(it.UpdateBy);
                        it.UpdateUser = new
                        {
                            UserId = it.UpdateBy,
                            CnName = updateUserInfo?.CnName ?? ""
                        };
                    }

                    // 处理管理员用户信息
                    if (!string.IsNullOrEmpty(it.ManagerUsers))
                    {
                        // 使用分号分割管理员用户ID
                        var managerUserIds = it.ManagerUsers.Split([';'], StringSplitOptions.RemoveEmptyEntries)
                            .Select(x => x.Trim())
                            .Where(x => !string.IsNullOrEmpty(x))
                            .ToList();

                        if (managerUserIds.Count > 0)
                        {
                            var managerUserList = new List<object>();

                            foreach (var userId in managerUserIds)
                            {
                                var userInfo = await userBaseInfoRepository.GetCacheValueAsync(userId);
                                managerUserList.Add(new
                                {
                                    UserId = userId,
                                    CnName = userInfo?.CnName ?? ""
                                });
                            }

                            it = it with { ManagerUserList = managerUserList };
                        }
                    }

                    return it;
                }).ToListAsync(cancellationToken);

            return new PageResult<GetMailTemplateListDto>
            {
                Data = lastResult,
                Total = totalCount,
                Page = request.PageIndex,
                PageSize = request.PageSize
            };
        }


    }
}
