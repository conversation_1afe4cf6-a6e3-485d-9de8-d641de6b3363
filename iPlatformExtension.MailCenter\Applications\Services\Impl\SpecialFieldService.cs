using iPlatformExtension.MailCenter.Applications.Models.Template;
using iPlatformExtension.MailCenter.Applications.Queries.TempField.SpecialField;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Services.Impl;

/// <summary>
/// 特殊字段处理服务实现
/// </summary>
internal sealed class SpecialFieldService(IMediator mediator) : ISpecialFieldService
{
    public async Task<Dictionary<string, string>> ProcessSpecialFieldAsync(
        Model.MailCenter.TempField field,
        List<MailDataGroup> dataGroups,
        CancellationToken cancellationToken)
    {
        // 根据字段的Key或Name来确定使用哪个特殊处理器
        var query = CreateSpecialFieldQuery(field, dataGroups);

        if (query != null)
        {
            return await mediator.Send(query, cancellationToken);
        }

        // 如果没有找到对应的特殊处理器，返回空结果
        return new Dictionary<string, string>();
    }

    /// <summary>
    /// 创建特殊字段查询
    /// </summary>
    private static ISpecialFieldQuery? CreateSpecialFieldQuery(
        Model.MailCenter.TempField field,
        List<MailDataGroup> dataGroups)
    {
        // 根据字段的Key或Name来判断使用哪个特殊处理器
        var fieldKey = field.Key?.ToLower() ?? string.Empty;

        // no1_contact 特殊字段处理
        if (fieldKey.Contains("no1_contact"))
        {
            return new No1ContactSpecialFieldQuery(field, dataGroups);
        }
        // task_fee 特殊字段处理
        if (fieldKey.Contains("task_fee") || fieldKey.Contains("list_task_fee") ||
            fieldKey.Contains("total_fee") || fieldKey.Contains("list_total_fee"))
        {
            return new TaskFeeSpecialFieldQuery(field, dataGroups);
        }
        // agency_fee_excluding_misc/misc_fee 特殊字段处理
        if (fieldKey.Contains("agency_fee_excluding_misc") || fieldKey.Contains("list_agency_fee_excluding_misc")
        || fieldKey.Contains("misc_fee") || fieldKey.Contains("list_misc_fee"))
        {
            return new AgencyFeeExcludingMiscSpecialFieldQuery(field, dataGroups);
        }
        // customer_mail 特殊字段处理
        if (fieldKey.Contains("customer_mail"))
        {
            return new CustomerMailSpecialFieldQuery(field, dataGroups);
        }
        // contact_list_mail 特殊字段处理
        if (fieldKey.Contains("contact_list_mail"))
        {
            return new ContactListMailSpecialFieldQuery(field, dataGroups);
        }

        return null;
    }
}
