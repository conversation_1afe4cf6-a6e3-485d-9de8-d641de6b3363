using System.Globalization;
using FreeSql;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenter.Applications.Queries.TempField.SpecialField;
using iPlatformExtension.Repository.Interface;
using Microsoft.Extensions.Logging;

namespace iPlatformExtension.MailCenter.Applications.Handlers.TempField.SpecialField;

/// <summary>
/// 普通特殊字段查询处理器
/// </summary>
internal sealed class NormalSpecialFieldQueryHandler(
    IFreeSql<PlatformFreeSql> freeSql,
    ICustomerRepository customerRepository,
    IApplyTypeRepository applyTypeRepository,
    IBaseCaseStatusRepository baseCaseStatusRepository,
    IBaseCtrlProcRepository baseCtrlProcRepository,
    ILogger<NormalSpecialFieldQueryHandler> logger)
    : BaseSpecialFieldQueryHandler<NormalSpecialFieldQuery>(freeSql, customerRepository, applyTypeRepository, baseCaseStatusRepository, baseCtrlProcRepository)
{
    public override async Task<Dictionary<string, string>> Handle(NormalSpecialFieldQuery request, CancellationToken cancellationToken)
    {
        var result = new Dictionary<string, string>();

        try
        {
            var fieldKey = request.Field.Key?.ToLower() ?? string.Empty;

            // 处理当前日期相关字段
            if (fieldKey.Contains("datetime_now"))
            {
                var currentDateTime = DateTime.Now;
                string dateValue;

                // 默认格式（根据字段配置的Format）
                dateValue = await ProcessFieldValueWithLogoAsync(currentDateTime, request.Field, cancellationToken);

                if (!string.IsNullOrEmpty(dateValue))
                {
                    result[request.Field.Key!] = dateValue;
                }
            }
            // 处理选中数量统计字段
            else if (fieldKey == "selected_count")
            {
                var count = request.DataGroups?.Count ?? 0;
                var countValue = await ProcessFieldValueWithLogoAsync(count, request.Field, cancellationToken);

                if (!string.IsNullOrEmpty(countValue))
                {
                    result[request.Field.Key!] = countValue;
                }
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "NormalSpecialFieldQueryHandler处理发生异常. FieldKey: {FieldKey}",
                request.Field.Key);
        }

        return result;
    }
}
