using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenter.Applications.Models.Count;
using iPlatformExtension.MailCenter.Applications.Queries.Count;
using iPlatformExtension.MailCenter.HostedService;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using MediatR;
using Microsoft.Extensions.Logging;
using static iPlatformExtension.Model.Enum.SysEnum;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Count
{
    public class GetSendReaderListCountHandler(
         ILogger<InitMailCountService> logger,
        IFreeSql<MailCenterFreeSql> freeSql,
        IRedisCache<RedisCacheOptionsBase> redisCache,
        IFreeSql<PlatformFreeSql> platformFreeSql
        ) : IRequestHandler<GetSendReaderListCountQuery, Dictionary<string, CacheValueModel>>
    {
        public async Task<Dictionary<string, CacheValueModel>> Handle(GetSendReaderListCountQuery request, CancellationToken cancellationToken)
        {
            try
            {
                var defaultDict = request.defaultDict;
                var cacheTime = request._cacheTime ?? TimeSpan.FromMinutes(5);
                logger.LogInformation("开始统计待阅读邮件数量");
                var count = await freeSql
                    .Select<MailReaderList, MailSend>()
                    .LeftJoin(o => o.t1.MailId == o.t2.MailId)
                    .Where(o =>
                        o.t1.Status == (int)ReaderStatusEnum.ToRead
                        && o.t2.Status >= SysEnum.ReceiveFileType.Handle.GetHashCode()
                    )
                    .WithLock()
                    .GroupBy(o => o.t1.UserId)
                    .ToDictionaryAsync(
                        it => new CacheValueModel { Count = it.Count(), CreateTime = DateTime.Now },
                        cancellationToken
                    );

                // 将查询结果合并到默认字典中
                foreach (var item in count)
                {
                    defaultDict[item.Key] = item.Value;
                }

                await redisCache.RemoveCacheKeyAsync("SendReaderList", cancellationToken);
                await redisCache.SetCacheValuesAsync(
                    "SendReaderList",
                    defaultDict,
                    cacheTime,
                    cancellationToken: cancellationToken
                );

                logger.LogInformation("待阅读邮件数量统计完成，共{Count}个用户有待阅读邮件", count.Count);
                return defaultDict;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "统计待阅读邮件数量异常");
                return request.defaultDict;
            }
        }
    }
} 