﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenter.Applications.Models.Count;
using iPlatformExtension.MailCenter.Applications.Queries.Count;
using iPlatformExtension.MailCenter.HostedService;
using iPlatformExtension.Model.MailCenter;
using MediatR;
using Microsoft.Extensions.Logging;
using static iPlatformExtension.Model.Enum.SysEnum;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Count
{
    public class GetAuditMailCountHandler(
         ILogger<InitMailCountService> logger,
        IFreeSql<MailCenterFreeSql> freeSql,
        IRedisCache<RedisCacheOptionsBase> redisCache,
        IFreeSql<PlatformFreeSql> platformFreeSql
        ) : IRequestHandler<GetAuditSendMailCountQuery, Dictionary<string, CacheValueModel>>
    {
        public async Task<Dictionary<string, CacheValueModel>> Handle(GetAuditSendMailCountQuery request, CancellationToken cancellationToken)
        {
            try
            {
                var defaultDict = request.defaultDict;
                var cacheTime = request._cacheTime ?? TimeSpan.FromMinutes(5);
                logger.LogInformation("开始统计待审核邮件数量");
                var count = await freeSql
                    .Select<MailSend, MailSendFlow>()
                    .LeftJoin(o => o.t1.MailId == o.t2.MailId)
                    .Where(o =>
                        o.t1.Status == SendStatusType.Reviewing.GetHashCode()
                    )
                    .WithLock()
                    .GroupBy(o => o.t2.AuditUser)
                    .ToDictionaryAsync(
                        it => new CacheValueModel { Count = it.Count(), CreateTime = DateTime.Now },
                        cancellationToken
                    );

                // 将查询结果合并到默认字典中
                foreach (var item in count)
                {
                    defaultDict[item.Key] = item.Value;
                }

                await redisCache.RemoveCacheKeyAsync("AuditSendMailCount", cancellationToken);
                await redisCache.SetCacheValuesAsync(
                    "AuditSendMailCount",
                    defaultDict,
                    cacheTime,
                    cancellationToken: cancellationToken
                );

                logger.LogInformation("待审核邮件数量完成，共{UserCount}个", count.Count);
                return defaultDict;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "待审核邮件数量统计统异常");
                return request.defaultDict;
            }
        }
    }
} 